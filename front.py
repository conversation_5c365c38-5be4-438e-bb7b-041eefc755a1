from flask import Flask, render_template_string, request, jsonify, session, redirect, url_for, send_from_directory
import os
import json
import hashlib
import sqlite3
from datetime import datetime, timedelta
import random
from werkzeug.security import generate_password_hash, check_password_hash
from functools import wraps
import mimetypes
import sys
import time
from threading import Thread

app = Flask(__name__)
app.secret_key = 'spring_gallery_secret_key_2024'

# 全局变量存储扫描状态
scan_progress = {'current': 0, 'total': 0, 'status': '准备扫描...', 'completed': False}

# 数据库初始化
def init_db():
    conn = sqlite3.connect('gallery.db')
    c = conn.cursor()
    
    # 用户表
    c.execute('''CREATE TABLE IF NOT EXISTS users
                 (id INTEGER PRIMARY KEY AUTOINCREMENT,
                  username TEXT UNIQUE NOT NULL,
                  email TEXT UNIQUE NOT NULL,
                  password_hash TEXT NOT NULL,
                  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                  avatar TEXT DEFAULT '/static/default_avatar.jpg')''')
    
    # 图片表
    c.execute('''CREATE TABLE IF NOT EXISTS images
                 (id INTEGER PRIMARY KEY AUTOINCREMENT,
                  filename TEXT NOT NULL,
                  path TEXT NOT NULL,
                  full_path TEXT NOT NULL,
                  category TEXT NOT NULL,
                  album TEXT NOT NULL,
                  file_size INTEGER,
                  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP)''')
    
    # 收藏表
    c.execute('''CREATE TABLE IF NOT EXISTS favorites
                 (id INTEGER PRIMARY KEY AUTOINCREMENT,
                  user_id INTEGER,
                  image_id INTEGER,
                  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                  FOREIGN KEY (user_id) REFERENCES users (id),
                  FOREIGN KEY (image_id) REFERENCES images (id))''')

    # 图集收藏表
    c.execute('''CREATE TABLE IF NOT EXISTS album_favorites
                 (id INTEGER PRIMARY KEY AUTOINCREMENT,
                  user_id INTEGER,
                  category TEXT,
                  album TEXT,
                  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                  FOREIGN KEY (user_id) REFERENCES users (id),
                  UNIQUE(user_id, category, album))''')
    
    # 评论表
    c.execute('''CREATE TABLE IF NOT EXISTS comments
                 (id INTEGER PRIMARY KEY AUTOINCREMENT,
                  user_id INTEGER,
                  image_id INTEGER,
                  content TEXT,
                  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                  FOREIGN KEY (user_id) REFERENCES users (id),
                  FOREIGN KEY (image_id) REFERENCES images (id))''')
    
    # 评分表
    c.execute('''CREATE TABLE IF NOT EXISTS ratings
                 (id INTEGER PRIMARY KEY AUTOINCREMENT,
                  user_id INTEGER,
                  image_id INTEGER,
                  rating INTEGER CHECK(rating >= 1 AND rating <= 5),
                  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                  FOREIGN KEY (user_id) REFERENCES users (id),
                  FOREIGN KEY (image_id) REFERENCES images (id))''')
    
    # 创建索引
    c.execute('CREATE INDEX IF NOT EXISTS idx_images_category ON images(category)')
    c.execute('CREATE INDEX IF NOT EXISTS idx_images_album ON images(album)')
    c.execute('CREATE INDEX IF NOT EXISTS idx_images_category_album ON images(category, album)')
    c.execute('CREATE INDEX IF NOT EXISTS idx_favorites_user ON favorites(user_id)')
    c.execute('CREATE INDEX IF NOT EXISTS idx_favorites_image ON favorites(image_id)')
    c.execute('CREATE INDEX IF NOT EXISTS idx_comments_image ON comments(image_id)')
    c.execute('CREATE INDEX IF NOT EXISTS idx_comments_user ON comments(user_id)')
    
    conn.commit()
    conn.close()

def print_progress_bar(current, total, status="", bar_length=50):
    """打印进度条"""
    if total == 0:
        percent = 0
    else:
        percent = current / total
    
    filled_length = int(bar_length * percent)
    bar = '█' * filled_length + '░' * (bar_length - filled_length)
    
    # 清除当前行并打印新的进度条
    sys.stdout.write(f'\r🌸 [{bar}] {percent:.1%} ({current}/{total}) {status}')
    sys.stdout.flush()

def get_existing_albums():
    """获取数据库中已存在的图集"""
    conn = sqlite3.connect('gallery.db')
    c = conn.cursor()
    c.execute('SELECT DISTINCT category, album FROM images')
    existing = set()
    for row in c.fetchall():
        existing.add((row[0], row[1]))
    conn.close()
    return existing

def check_album_integrity(category, album, base_path):
    """检查图集完整性，通过检查第一个图片文件是否存在"""
    album_path = os.path.join(base_path, category, album)
    if not os.path.exists(album_path):
        return False
    
    # 获取图集中的第一个图片文件
    image_files = []
    for file in os.listdir(album_path):
        if file.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.webp')):
            image_files.append(file)
    
    if not image_files:
        return False
    
    # 检查第一个图片文件是否存在
    first_image = sorted(image_files)[0]
    first_image_path = os.path.join(album_path, first_image)
    return os.path.exists(first_image_path)

def scan_and_store_images():
    """增量扫描图片并存储到数据库"""
    global scan_progress
    
    base_path = 'downloaded'
    categories = ['korea', 'cosplay', 'japan', 'gravure', 'chinese', 'thailand']
    
    print("\n🌸 开始增量扫描图片文件...")
    
    # 获取已存在的图集
    existing_albums = get_existing_albums()
    print(f"📊 数据库中已有 {len(existing_albums)} 个图集")
    
    # 统计需要扫描的文件
    scan_progress['status'] = '分析需要扫描的内容...'
    scan_progress['current'] = 0
    scan_progress['total'] = 0
    
    albums_to_scan = []
    total_files = 0
    
    for category in categories:
        category_path = os.path.join(base_path, category)
        if os.path.exists(category_path):
            for album in os.listdir(category_path):
                album_path = os.path.join(category_path, album)
                if os.path.isdir(album_path):
                    # 检查是否需要扫描这个图集
                    need_scan = False
                    
                    if (category, album) in existing_albums:
                        # 已存在，检查完整性
                        if not check_album_integrity(category, album, base_path):
                            print(f"⚠️  图集 {category}/{album} 不完整，需要重新扫描")
                            need_scan = True
                    else:
                        # 不存在，需要扫描
                        need_scan = True
                    
                    if need_scan:
                        album_files = []
                        for file in os.listdir(album_path):
                            if file.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.webp')):
                                album_files.append(file)
                        
                        if album_files:
                            albums_to_scan.append((category, album, album_files))
                            total_files += len(album_files)
    
    scan_progress['total'] = total_files
    
    if total_files == 0:
        print("✅ 所有图集都已是最新状态，无需扫描")
        scan_progress['status'] = '所有内容都是最新的'
        scan_progress['completed'] = True
        return
    
    print(f"📊 需要扫描 {len(albums_to_scan)} 个图集，共 {total_files} 个文件")
    
    conn = sqlite3.connect('gallery.db')
    c = conn.cursor()
    
    processed = 0
    
    # 扫描需要更新的图集
    for category, album, files in albums_to_scan:
        scan_progress['status'] = f'扫描 {category}/{album}...'
        
        # 如果图集已存在但不完整，先删除旧记录
        if (category, album) in existing_albums:
            c.execute('DELETE FROM images WHERE category = ? AND album = ?', (category, album))
        
        album_path = os.path.join(base_path, category, album)
        
        for file in files:
            full_path = os.path.join(album_path, file)
            # BUG FIX: 正确处理URL编码，确保中文和特殊字符能正确访问
            import urllib.parse
            try:
                encoded_category = urllib.parse.quote(category, safe='')
                encoded_album = urllib.parse.quote(album, safe='')
                encoded_file = urllib.parse.quote(file, safe='')
                web_path = f'/images/{encoded_category}/{encoded_album}/{encoded_file}'
            except Exception as e:
                print(f"URL编码失败: {e}, 使用原始路径")
                web_path = f'/images/{category}/{album}/{file}'

            # 获取文件大小
            try:
                file_size = os.path.getsize(full_path)
            except:
                file_size = 0

            # 存储到数据库
            c.execute('''INSERT INTO images
                         (filename, path, full_path, category, album, file_size)
                         VALUES (?, ?, ?, ?, ?, ?)''',
                     (file, web_path, full_path, category, album, file_size))
            
            processed += 1
            scan_progress['current'] = processed
            
            # 更新进度条
            print_progress_bar(processed, total_files, f'扫描 {category}/{album}')
            
            # 每100个文件提交一次
            if processed % 100 == 0:
                conn.commit()
    
    # 最终提交
    conn.commit()
    conn.close()
    
    scan_progress['status'] = '扫描完成！'
    scan_progress['completed'] = True
    
    print(f"\n✅ 增量扫描完成！新增/更新 {processed} 个图片文件")
    print("📁 最新分类统计:")
    
    # 显示统计信息
    conn = sqlite3.connect('gallery.db')
    c = conn.cursor()
    for category in categories:
        c.execute('SELECT COUNT(*) FROM images WHERE category = ?', (category,))
        count = c.fetchone()[0]
        c.execute('SELECT COUNT(DISTINCT album) FROM images WHERE category = ?', (category,))
        albums = c.fetchone()[0]
        if count > 0:
            print(f"   🌸 {get_category_name(category)}: {albums}个图集, {count}张图片")
    conn.close()

def get_category_name(category):
    """获取分类中文名"""
    names = {
        'korea': '韩系风格',
        'cosplay': '角色扮演',
        'japan': '日系清新',
        'gravure': '专业写真',
        'chinese': '中式古典',
        'thailand': '泰式风情'
    }
    return names.get(category, category)

# 登录验证装饰器
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

# 从数据库获取图片数据
def get_images_from_db(category=None, album=None, search=None, limit=None, offset=None):
    conn = sqlite3.connect('gallery.db')
    c = conn.cursor()
    
    query = 'SELECT id, filename, path, full_path, category, album, file_size, created_at FROM images WHERE 1=1'
    params = []
    
    if category:
        query += ' AND category = ?'
        params.append(category)
    
    if album:
        query += ' AND album = ?'
        params.append(album)
    
    if search:
        query += ' AND (album LIKE ? OR filename LIKE ?)'
        params.extend([f'%{search}%', f'%{search}%'])
    
    query += ' ORDER BY created_at DESC'
    
    if limit:
        query += ' LIMIT ?'
        params.append(limit)
    
    if offset:
        query += ' OFFSET ?'
        params.append(offset)
    
    c.execute(query, params)
    results = c.fetchall()
    conn.close()
    
    images = []
    for row in results:
        images.append({
            'id': row[0],
            'filename': row[1],
            'path': row[2],
            'full_path': row[3],
            'category': row[4],
            'album': row[5],
            'file_size': row[6],
            'created_at': row[7]
        })
    
    return images

# 获取图集列表
def get_albums_from_db(category=None):
    conn = sqlite3.connect('gallery.db')
    c = conn.cursor()
    
    if category:
        query = '''SELECT category, album, COUNT(*) as image_count,
                           MIN(path) as cover_image, MIN(created_at) as created_at
                   FROM images 
                   WHERE category = ?
                   GROUP BY category, album 
                   ORDER BY created_at DESC'''
        c.execute(query, (category,))
    else:
        query = '''SELECT category, album, COUNT(*) as image_count,
                           MIN(path) as cover_image, MIN(created_at) as created_at
                   FROM images 
                   GROUP BY category, album 
                   ORDER BY created_at DESC'''
        c.execute(query)
    
    results = c.fetchall()
    conn.close()
    
    albums = []
    for row in results:
        albums.append({
            'category': row[0],
            'album': row[1],
            'image_count': row[2],
            'cover_image': row[3],
            'created_at': row[4]
        })
    
    return albums

# BUG FIX: 重构此函数以支持高效的过滤、排序和分页
def get_user_favorites(user_id, category=None, sort='newest', limit=None, offset=None):
    """
    获取用户收藏的图片，支持过滤、排序和分页
    """
    conn = sqlite3.connect('gallery.db')
    c = conn.cursor()
    
    base_query = '''FROM favorites f 
                    JOIN images i ON f.image_id = i.id 
                    WHERE f.user_id = ?'''
    params = [user_id]
    
    # 添加分类过滤
    if category:
        base_query += ' AND i.category = ?'
        params.append(category)

    # 获取总数
    count_query = f'SELECT COUNT(f.id) {base_query}'
    c.execute(count_query, params)
    total_count = c.fetchone()[0]

    # 构建主查询
    query = f'''SELECT i.id, i.filename, i.path, i.full_path, i.category, i.album, i.file_size, f.created_at as favorited_at
                {base_query}'''

    # 添加排序
    sort_map = {
        'newest': 'f.created_at DESC',
        'oldest': 'f.created_at ASC',
        'album': 'i.album ASC, i.filename ASC'
    }
    order_by_clause = sort_map.get(sort, 'f.created_at DESC')
    query += f' ORDER BY {order_by_clause}'

    # 添加分页
    if limit is not None:
        query += ' LIMIT ?'
        params.append(limit)
    
    if offset is not None:
        query += ' OFFSET ?'
        params.append(offset)
    
    c.execute(query, params)
    results = c.fetchall()
    conn.close()
    
    favorites = []
    for row in results:
        favorites.append({
            'id': row[0],
            'filename': row[1],
            'path': row[2],
            'full_path': row[3],
            'category': row[4],
            'album': row[5],
            'file_size': row[6],
            'favorited_at': row[7]
        })
    
    return favorites, total_count


# 启动页面模板
STARTUP_TEMPLATE = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>启动中 - 春色写真馆</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 25%, #f0fff4 50%, #fff8dc 75%, #fdf5e6 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #5d4e37;
        }
        
        .startup-container {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            padding: 4rem;
            border-radius: 30px;
            box-shadow: 0 30px 60px rgba(255, 182, 193, 0.3);
            text-align: center;
            max-width: 600px;
            width: 90%;
            position: relative;
            overflow: hidden;
        }
        
        .startup-container::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 182, 193, 0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        
        .logo {
            font-size: 4rem;
            margin-bottom: 2rem;
            background: linear-gradient(45deg, #ff69b4, #ffa500, #98fb98);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            position: relative;
            z-index: 1;
        }
        
        .startup-title {
            font-size: 2rem;
            margin-bottom: 1rem;
            color: #8b4513;
            position: relative;
            z-index: 1;
        }
        
        .startup-subtitle {
            font-size: 1.2rem;
            color: #cd853f;
            margin-bottom: 3rem;
            position: relative;
            z-index: 1;
        }
        
        .progress-container {
            margin: 2rem 0;
            position: relative;
            z-index: 1;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: rgba(255, 182, 193, 0.3);
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 1rem;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(45deg, #ff69b4, #ffa500);
            border-radius: 10px;
            transition: width 0.3s ease;
            width: 0%;
        }
        
        .progress-text {
            font-size: 1rem;
            color: #8b4513;
            margin-bottom: 0.5rem;
        }
        
        .progress-status {
            font-size: 0.9rem;
            color: #cd853f;
            font-style: italic;
        }
        
        .loading-dots {
            display: inline-block;
            position: relative;
            width: 80px;
            height: 20px;
            margin-left: 10px;
        }
        
        .loading-dots div {
            position: absolute;
            top: 8px;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #ff69b4;
            animation: loading-dots 1.2s linear infinite;
        }
        
        .loading-dots div:nth-child(1) { left: 8px; animation-delay: 0s; }
        .loading-dots div:nth-child(2) { left: 32px; animation-delay: -0.4s; }
        .loading-dots div:nth-child(3) { left: 56px; animation-delay: -0.8s; }
        
        @keyframes loading-dots {
            0%, 80%, 100% {
                transform: scale(0);
                opacity: 0.5;
            }
            40% {
                transform: scale(1);
                opacity: 1;
            }
        }
        
        .startup-complete {
            display: none;
            color: #2e7d32;
            font-size: 1.2rem;
            font-weight: bold;
            margin-top: 2rem;
        }
        
        .startup-complete.show {
            display: block;
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .redirect-info {
            margin-top: 2rem;
            padding: 1rem;
            background: rgba(46, 125, 50, 0.1);
            border-radius: 10px;
            color: #2e7d32;
            display: none;
        }
        
        .redirect-info.show {
            display: block;
            animation: fadeIn 0.5s ease-in;
        }
        
        .skip-btn {
            margin-top: 2rem;
            padding: 1rem 2rem;
            background: linear-gradient(45deg, #98fb98, #87ceeb);
            color: #2f4f4f;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
            position: relative;
            z-index: 1;
        }
        
        .skip-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(152, 251, 152, 0.4);
        }
    </style>
</head>
<body>
    <div class="startup-container">
        <div class="logo">🌸</div>
        <h1 class="startup-title">春色写真馆</h1>
        <p class="startup-subtitle">正在初始化系统...</p>
        
        <div class="progress-container">
            <div class="progress-text" id="progressText">准备扫描图片文件...</div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="progress-status" id="progressStatus">
                系统启动中
                <div class="loading-dots">
                    <div></div>
                    <div></div>
                    <div></div>
                </div>
            </div>
        </div>
        
        <div class="startup-complete" id="startupComplete">
            ✅ 系统初始化完成！
        </div>
        
        <div class="redirect-info" id="redirectInfo">
            <p>🎉 即将跳转到主页面...</p>
            <p>如果没有自动跳转，请 <a href="/" style="color: #2e7d32; font-weight: bold;">点击这里</a></p>
        </div>
        
        <button class="skip-btn" onclick="skipToMain()" id="skipBtn">跳过扫描，直接进入</button>
    </div>

    <script>
        let checkInterval;
        let redirectTimeout;
        
        function updateProgress() {
            fetch('/api/scan-progress')
                .then(response => response.json())
                .then(data => {
                    const progressFill = document.getElementById('progressFill');
                    const progressText = document.getElementById('progressText');
                    const progressStatus = document.getElementById('progressStatus');
                    const startupComplete = document.getElementById('startupComplete');
                    const redirectInfo = document.getElementById('redirectInfo');
                    
                    if (data.total > 0) {
                        const percent = (data.current / data.total) * 100;
                        progressFill.style.width = percent + '%';
                        progressText.textContent = `扫描进度: ${data.current}/${data.total} (${percent.toFixed(1)}%)`;
                    } else if (data.completed) {
                        progressFill.style.width = '100%';
                        progressText.textContent = '扫描完成！';
                    }
                    
                    progressStatus.innerHTML = data.status + 
                        (data.completed ? '' : '<div class="loading-dots"><div></div><div></div><div></div></div>');
                    
                    if (data.completed) {
                        clearInterval(checkInterval);
                        startupComplete.classList.add('show');
                        redirectInfo.classList.add('show');
                        document.getElementById('skipBtn').style.display = 'none';
                        
                        // 2秒后跳转到主页
                        redirectTimeout = setTimeout(() => {
                            window.location.href = '/';
                        }, 2000);
                    }
                })
                .catch(error => {
                    console.error('获取进度失败:', error);
                    // 如果获取进度失败，也允许跳转
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 3000);
                });
        }
        
        function skipToMain() {
            // BUG FIX: 调用后端API跳过扫描，然后跳转
            const skipBtn = document.getElementById('skipBtn');
            if (skipBtn) {
                skipBtn.disabled = true;
                skipBtn.textContent = '跳转中...';
            }

            fetch('/api/skip-scan', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    cleanupAndRedirect();
                } else {
                    throw new Error('Skip scan failed');
                }
            })
            .catch(error => {
                console.error('跳过扫描失败:', error);
                // 即使失败也允许跳转
                cleanupAndRedirect();
            });
        }

        function cleanupAndRedirect() {
            if (checkInterval) {
                clearInterval(checkInterval);
            }
            if (redirectTimeout) {
                clearTimeout(redirectTimeout);
            }
            window.location.href = '/';
        }
        
        // 页面加载后开始检查进度
        document.addEventListener('DOMContentLoaded', function() {
            updateProgress();
            checkInterval = setInterval(updateProgress, 1000);
            
            // BUG FIX: 立即显示跳过按钮，让用户可以随时跳过
            const skipBtn = document.getElementById('skipBtn');
            skipBtn.style.display = 'block';
        });
    </script>
</body>
</html>'''

# 图集详情页面模板 - 修复后的版本
ALBUM_TEMPLATE = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ album }} - {{ category_name }} - 春色写真馆</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 25%, #f0fff4 50%, #fff8dc 75%, #fdf5e6 100%);
            min-height: 100vh;
            color: #5d4e37;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            padding: 1rem 0;
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
            box-shadow: 0 2px 20px rgba(255, 182, 193, 0.3);
        }
        
        .nav {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
        }
        
        .logo {
            font-size: 2rem;
            font-weight: bold;
            background: linear-gradient(45deg, #ff69b4, #ffa500, #98fb98);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        .nav-links {
            display: flex;
            gap: 2rem;
            align-items: center;
        }
        
        .nav-link {
            color: #8b4513;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .nav-link:hover {
            background: linear-gradient(45deg, rgba(255, 182, 193, 0.3), rgba(255, 218, 185, 0.3));
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 182, 193, 0.4);
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 2px solid #ffb6c1;
            object-fit: cover;
        }
        
        .main-content {
            margin-top: 100px;
            padding: 2rem;
            max-width: 1400px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .breadcrumb {
            background: rgba(255, 255, 255, 0.8);
            padding: 1rem 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            font-size: 0.9rem;
        }
        
        .breadcrumb a {
            color: #8b4513;
            text-decoration: none;
            transition: color 0.3s ease;
        }
        
        .breadcrumb a:hover {
            color: #ff69b4;
        }
        
        .album-header {
            background: linear-gradient(135deg, rgba(255, 182, 193, 0.1), rgba(255, 218, 185, 0.1));
            padding: 3rem 2rem;
            border-radius: 30px;
            margin-bottom: 3rem;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .album-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 182, 193, 0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        
        .album-title {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #ff69b4, #ffa500, #98fb98, #dda0dd);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            position: relative;
            z-index: 1;
        }
        
        .album-info {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 1.5rem;
            margin-top: 1.5rem;
            position: relative;
            z-index: 1;
            flex-wrap: wrap;
        }
        
        .info-item {
            background: rgba(255, 255, 255, 0.8);
            padding: 0.6rem 1.2rem;
            border-radius: 15px;
            font-size: 0.9rem;
            color: #8b4513;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 2.5rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(5px);
        }

        .album-favorite-btn {
            background: linear-gradient(45deg, #ff69b4, #ffa500);
            color: white;
            border: none;
            padding: 0.6rem 1.2rem;
            border-radius: 15px;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(255, 105, 180, 0.3);
            min-height: 2.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .album-favorite-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 105, 180, 0.4);
        }

        .album-favorite-btn.favorited {
            background: linear-gradient(45deg, #ff1493, #ff6347);
        }
        
        .gallery-controls {
            background: rgba(255, 255, 255, 0.9);
            padding: 1rem 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }
        
        .view-controls {
            display: flex;
            gap: 1rem;
            align-items: center;
        }
        
        .view-btn {
            padding: 0.5rem 1rem;
            border: 1px solid #ffb6c1;
            background: rgba(255, 255, 255, 0.9);
            color: #8b4513;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .view-btn.active, .view-btn:hover {
            background: linear-gradient(45deg, #ff69b4, #ffa500);
            color: white;
            transform: translateY(-2px);
        }
        
        .batch-actions {
            display: flex;
            gap: 1rem;
        }
        
        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #ff69b4, #ffa500);
            color: white;
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #98fb98, #87ceeb);
            color: #2f4f4f;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 105, 180, 0.4);
        }
        
        .gallery-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 3rem;
        }
        
        .gallery-grid.large {
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 2rem;
        }
        
        .gallery-grid.small {
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 1rem;
        }
        
        .image-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            overflow: hidden;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            box-shadow: 0 4px 15px rgba(255, 182, 193, 0.2);
        }
        
        .image-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(255, 182, 193, 0.4);
        }
        
        .image-card img {
            width: 100%;
            height: 200px;
            object-fit: cover;
            transition: transform 0.3s ease;
        }
        
        .gallery-grid.large .image-card img {
            height: 250px;
        }
        
        .gallery-grid.small .image-card img {
            height: 150px;
        }
        
        .image-card:hover img {
            transform: scale(1.05);
        }
        
        .image-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.7) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
            display: flex;
            align-items: flex-end;
            padding: 1rem;
        }
        
        .image-card:hover .image-overlay {
            opacity: 1;
        }
        
        .image-actions {
            display: flex;
            gap: 0.5rem;
        }
        
        .action-btn {
            background: rgba(255, 255, 255, 0.9);
            border: none;
            padding: 0.5rem;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .action-btn:hover {
            background: #ff69b4;
            color: white;
            transform: scale(1.1);
        }
        
        .action-btn.favorited {
            background: #ff69b4;
            color: white;
        }
        
        .loading {
            text-align: center;
            padding: 2rem;
            font-size: 1.2rem;
            color: #8b4513;
        }
        
        .load-more {
            text-align: center;
            margin: 2rem 0;
        }
        
        .load-more-btn {
            padding: 1rem 2rem;
            background: linear-gradient(45deg, #ff69b4, #ffa500);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .load-more-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 105, 180, 0.4);
        }
        
        .load-more-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        /* 图片查看器模态框 */
        .modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.95);
            backdrop-filter: blur(5px);
        }
        
        .modal-content {
            position: relative;
            margin: 0 auto;
            max-width: 95%;
            max-height: 95%;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100vh;
            padding: 2% 0;
        }
        
        .modal-image {
            max-width: 100%;
            max-height: 90vh;
            object-fit: contain;
            border-radius: 10px;
        }
        
        .modal-nav {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.9);
            border: none;
            padding: 1rem;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.5rem;
            transition: all 0.3s ease;
            z-index: 2001;
        }
        
        .modal-nav:hover {
            background: #ff69b4;
            color: white;
            transform: translateY(-50%) scale(1.1);
        }
        
        .modal-prev {
            left: 20px;
        }
        
        .modal-next {
            right: 20px;
        }
        
        .modal-close {
            position: absolute;
            top: 20px;
            right: 30px;
            color: #fff;
            font-size: 40px;
            font-weight: bold;
            cursor: pointer;
            background: rgba(0, 0, 0, 0.5);
            border-radius: 50%;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            z-index: 2001;
        }
        
        .modal-close:hover {
            background: rgba(255, 105, 180, 0.8);
            transform: scale(1.1);
        }
        
        .modal-info {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 1rem 2rem;
            border-radius: 25px;
            text-align: center;
            z-index: 2001;
        }
        
        /* 手机端适配 - 修复后的版本 */
        @media (max-width: 768px) {
            .header {
                padding: 0.5rem 0;
                position: fixed;
                top: 0;
                width: 100%;
                z-index: 1000;
            }
            
            .nav {
                flex-direction: column;
                gap: 0.5rem;
                padding: 0 1rem;
            }
            
            .logo {
                font-size: 1.5rem;
            }
            
            .nav-links {
                gap: 1rem;
                flex-wrap: wrap;
                justify-content: center;
            }
            
            .main-content {
                margin-top: 140px; /* 增加顶部边距避免重叠 */
                padding: 1rem;
            }
            
            .album-title {
                font-size: 2rem;
            }
            
            .album-info {
                flex-direction: column;
                gap: 1rem;
            }
            
            .gallery-controls {
                flex-direction: column;
                align-items: stretch;
            }
            
            .view-controls, .batch-actions {
                justify-content: center;
            }
            
            .gallery-grid {
                grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
                gap: 1rem;
            }
            
            /* 手机端模态框样式 - 修复版本 */
            .modal-content {
                height: 100vh;
                padding: 5% 0;
                align-items: center;
                justify-content: center;
            }
            
            .modal-image {
                max-width: 95%;
                max-height: 85vh;
                object-fit: contain;
                border-radius: 10px;
            }
            
            /* 手机端模态框导航按钮 - 修复版本 */
            .modal-nav {
                padding: 0.8rem;
                font-size: 1.2rem;
                width: 50px;
                height: 50px;
                display: flex;
                align-items: center;
                justify-content: center;
                background: rgba(255, 255, 255, 0.9);
                backdrop-filter: blur(5px);
            }
            
            .modal-prev {
                left: 10px;
            }
            
            .modal-next {
                right: 10px;
            }
            
            .modal-close {
                top: 10px;
                right: 15px;
                width: 50px;
                height: 50px;
                font-size: 30px;
                background: rgba(0, 0, 0, 0.7);
                backdrop-filter: blur(5px);
            }
            
            .modal-info {
                bottom: 10px;
                padding: 0.8rem 1.5rem;
                font-size: 0.9rem;
                background: rgba(0, 0, 0, 0.8);
                backdrop-filter: blur(5px);
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <nav class="nav">
            <a href="/" class="logo" style="text-decoration: none; color: inherit;">🌸 春色写真馆</a>
            <div class="nav-links">
                <a href="/" class="nav-link">首页</a>
                <a href="/gallery" class="nav-link">图库</a>
                <a href="/favorites" class="nav-link">收藏</a>
                {% if session.user_id %}
                    <div class="user-info">
                        <img src="{{ session.avatar or '/static/default_avatar.jpg' }}" alt="头像" class="avatar">
                        <span>{{ session.username }}</span>
                        <a href="/logout" class="nav-link">退出</a>
                    </div>
                {% else %}
                    <a href="/login" class="nav-link">登录</a>
                    <a href="/register" class="nav-link">注册</a>
                {% endif %}
            </div>
        </nav>
    </header>

    <main class="main-content">
        <div class="breadcrumb">
            <a href="/">首页</a> > 
            <a href="/?category={{ category }}">{{ category_name }}</a> > 
            <span>{{ album }}</span>
        </div>

        <div class="album-header">
            <h1 class="album-title">{{ album }}</h1>
            <div class="album-info">
                <div class="info-item">📂 {{ category_name }}</div>
                <div class="info-item">📸 <span id="imageCount">加载中...</span> 张图片</div>
                <div class="info-item">📅 <span id="createDate">加载中...</span></div>
                {% if session.user_id %}
                <div class="info-item">
                    <button id="albumFavoriteBtn" class="album-favorite-btn" onclick="toggleAlbumFavorite()" title="收藏图集">
                        ❤️ 收藏图集
                    </button>
                </div>
                {% endif %}
            </div>
        </div>

        <div class="gallery-controls">
            <div class="view-controls">
                <button class="view-btn active" onclick="setViewSize('medium')" data-size="medium">中等</button>
                <button class="view-btn" onclick="setViewSize('large')" data-size="large">大图</button>
                <button class="view-btn" onclick="setViewSize('small')" data-size="small">小图</button>
            </div>
            <div class="batch-actions">
                {% if session.user_id %}
                <button class="btn btn-primary" onclick="favoriteAll()">❤️ 收藏全部</button>
                {% endif %}
                <button class="btn btn-secondary" onclick="downloadAll()">⬇️ 下载全部</button>
            </div>
        </div>

        <div class="gallery-grid" id="galleryGrid">
            <div class="loading">正在加载图片...</div>
        </div>

        <div class="load-more" id="loadMoreContainer" style="display: none;">
            <button class="load-more-btn" id="loadMoreBtn" onclick="loadMoreImages()">加载更多</button>
        </div>
    </main>

    <!-- 图片查看器模态框 -->
    <div id="imageModal" class="modal">
        <div class="modal-content">
            <span class="modal-close" onclick="closeModal()">&times;</span>
            <button class="modal-nav modal-prev" onclick="prevImage()">‹</button>
            <img class="modal-image" id="modalImage" src="/placeholder.svg" alt="">
            <button class="modal-nav modal-next" onclick="nextImage()">›</button>
            <div class="modal-info">
                <div id="modalInfo">1 / 10</div>
            </div>
        </div>
    </div>

    <script>
        let currentImages = [];
        let currentPage = 1;
        let itemsPerPage = 20;
        let loading = false;
        let hasMore = true;
        let currentModalIndex = 0;
        let userFavorites = new Set(); // 存储用户收藏的图片ID

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadImages();
            {% if session.user_id %}
            loadUserFavorites();
            checkAlbumFavoriteStatus();
            {% endif %}
        });

        // 检查图集收藏状态
        async function checkAlbumFavoriteStatus() {
            try {
                const response = await fetch(`/api/album-favorite-status?category={{ category }}&album={{ album }}`);
                const data = await response.json();
                updateAlbumFavoriteButton(data.is_favorited);
            } catch (error) {
                console.error('检查图集收藏状态失败:', error);
            }
        }

        // 更新图集收藏按钮状态
        function updateAlbumFavoriteButton(isFavorited) {
            const btn = document.getElementById('albumFavoriteBtn');
            if (btn) {
                if (isFavorited) {
                    btn.classList.add('favorited');
                    btn.innerHTML = '💖 已收藏图集';
                    btn.title = '取消收藏图集';
                } else {
                    btn.classList.remove('favorited');
                    btn.innerHTML = '❤️ 收藏图集';
                    btn.title = '收藏图集';
                }
            }
        }

        // 切换图集收藏状态
        async function toggleAlbumFavorite() {
            const btn = document.getElementById('albumFavoriteBtn');
            if (btn.disabled) return;

            btn.disabled = true;
            const originalText = btn.innerHTML;
            btn.innerHTML = '处理中...';

            try {
                const response = await fetch('/api/album-favorite', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        category: '{{ category }}',
                        album: '{{ album }}'
                    })
                });

                const data = await response.json();

                if (data.success) {
                    updateAlbumFavoriteButton(data.is_favorited);
                    showMessage(data.message, 'success');
                } else {
                    btn.innerHTML = originalText;
                    showMessage(data.message || '操作失败', 'error');
                }
            } catch (error) {
                console.error('切换图集收藏状态失败:', error);
                btn.innerHTML = originalText;
                showMessage('网络错误，请重试', 'error');
            } finally {
                btn.disabled = false;
            }
        }

        // 加载用户收藏状态
        async function loadUserFavorites() {
            try {
                const response = await fetch('/api/user-favorites');
                const favorites = await response.json();
                userFavorites = new Set(favorites.map(f => f.image_id.toString()));
            } catch (error) {
                console.error('加载收藏状态失败:', error);
            }
        }

        // 加载图片数据
        async function loadImages(append = false) {
            if (loading) return;
            loading = true;

            try {
                const response = await fetch(`/api/album-images?category={{ category }}&album={{ album|urlencode }}&page=${currentPage}&limit=${itemsPerPage}`);
                const data = await response.json();
                
                if (append) {
                    currentImages = currentImages.concat(data.images);
                } else {
                    currentImages = data.images;
                    document.getElementById('imageCount').textContent = data.total;
                    if (data.images.length > 0) {
                        document.getElementById('createDate').textContent = new Date(data.images[0].created_at).toLocaleDateString();
                    }
                }

                displayImages(append);
                
                hasMore = data.images.length === itemsPerPage;
                updateLoadMoreButton();
                
            } catch (error) {
                console.error('加载图片失败:', error);
                document.getElementById('galleryGrid').innerHTML = '<div class="loading">加载失败，请刷新页面重试</div>';
            } finally {
                loading = false;
            }
        }

        // 显示图片
        function displayImages(append = false) {
            const grid = document.getElementById('galleryGrid');
            
            if (!append) {
                grid.innerHTML = '';
            }

            const startIndex = append ? currentImages.length - itemsPerPage : 0;
            const imagesToShow = append ? currentImages.slice(startIndex) : currentImages;

            imagesToShow.forEach((image, index) => {
                const actualIndex = startIndex + index;
                const isFavorited = userFavorites.has(image.id.toString());
                
                const imageCard = document.createElement('div');
                imageCard.className = 'image-card';
                imageCard.innerHTML = `
                    <img src="${image.path}" alt="${image.filename}" loading="lazy"
                          onerror="this.src='/static/placeholder.jpg'">
                    <div class="image-overlay">
                        <div class="image-actions">
                            {% if session.user_id %}
                            <button class="action-btn ${isFavorited ? 'favorited' : ''}"
                                    onclick="event.stopPropagation(); toggleFavorite('${image.id}', this)"
                                    title="收藏"
                                    data-image-id="${image.id}">
                                ❤️
                            </button>
                            {% endif %}
                            <button class="action-btn" onclick="event.stopPropagation(); downloadImage('${image.path}', '${image.filename}')" title="下载">
                                ⬇️
                            </button>
                            <button class="action-btn" onclick="event.stopPropagation(); openModal(${actualIndex})" title="查看">
                                👁️
                            </button>
                        </div>
                    </div>
                `;
                grid.appendChild(imageCard);
            });
        }

        // 加载更多图片
        function loadMoreImages() {
            if (!hasMore || loading) return;
            currentPage++;
            loadImages(true);
        }

        // 更新加载更多按钮
        function updateLoadMoreButton() {
            const container = document.getElementById('loadMoreContainer');
            const btn = document.getElementById('loadMoreBtn');
            
            if (hasMore && currentImages.length > 0) {
                container.style.display = 'block';
                btn.disabled = loading;
                btn.textContent = loading ? '加载中...' : '加载更多';
            } else {
                container.style.display = 'none';
            }
        }

        // 设置视图大小
        function setViewSize(size) {
            const grid = document.getElementById('galleryGrid');
            const buttons = document.querySelectorAll('.view-btn');
            
            buttons.forEach(btn => btn.classList.remove('active'));
            document.querySelector(`[data-size="${size}"]`).classList.add('active');
            
            grid.className = `gallery-grid ${size}`;
        }



        // 打开模态框
        function openModal(index) {
            if (index < 0 || index >= currentImages.length) {
                console.error('Invalid image index:', index);
                return;
            }
            currentModalIndex = index;
            updateModal();
            const modal = document.getElementById('imageModal');
            if (modal) {
                modal.style.display = 'block';
                document.body.style.overflow = 'hidden';
            }
        }

        // 关闭模态框
        function closeModal() {
            const modal = document.getElementById('imageModal');
            if (modal) {
                modal.style.display = 'none';
                document.body.style.overflow = 'auto';
            }
        }

        // 更新模态框内容
        function updateModal() {
            if (currentImages.length === 0) return;
            
            const image = currentImages[currentModalIndex];
            document.getElementById('modalImage').src = image.path;
            document.getElementById('modalInfo').textContent = `${currentModalIndex + 1} / ${currentImages.length}`;
        }

        // 上一张图片
        function prevImage() {
            if (currentModalIndex > 0) {
                currentModalIndex--;
                updateModal();
            }
        }

        // 下一张图片
        function nextImage() {
            if (currentModalIndex < currentImages.length - 1) {
                currentModalIndex++;
                updateModal();
            }
        }

        // 切换收藏
        async function toggleFavorite(imageId, buttonElement) {
            try {
                const response = await fetch('/api/favorite', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        image_id: imageId
                    })
                });
                
                const result = await response.json();
                if (result.success) {
                    // 更新按钮状态
                    if (result.favorited) {
                        buttonElement.classList.add('favorited');
                        userFavorites.add(imageId.toString());
                    } else {
                        buttonElement.classList.remove('favorited');
                        userFavorites.delete(imageId.toString());
                    }
                    
                    // 显示提示
                    const message = result.favorited ? '已收藏' : '已取消收藏';
                    showToast(message);
                }
            } catch (error) {
                console.error('收藏操作失败:', error);
                showToast('操作失败，请重试');
            }
        }

        // 显示提示消息
        function showToast(message) {
            // 创建提示元素
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 1rem 2rem;
                border-radius: 25px;
                z-index: 3000;
                font-size: 1rem;
                pointer-events: none;
            `;
            toast.textContent = message;
            document.body.appendChild(toast);
            
            // 2秒后移除
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 2000);
        }

        // 下载图片
        function downloadImage(imagePath, filename) {
            const link = document.createElement('a');
            link.href = imagePath;
            link.download = filename;
            link.click();
        }

        // 收藏全部
        async function favoriteAll() {
            if (!confirm(`确定要收藏这个图集的所有 ${currentImages.length} 张图片吗？`)) {
                return;
            }
            
            let successCount = 0;
            for (const image of currentImages) {
                try {
                    const response = await fetch('/api/favorite', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            image_id: image.id
                        })
                    });
                    
                    const result = await response.json();
                    if (result.success && result.favorited) {
                        successCount++;
                        userFavorites.add(image.id.toString());
                    }
                } catch (error) {
                    console.error('收藏失败:', error);
                }
            }
            
            // 更新所有收藏按钮状态
            document.querySelectorAll('.action-btn[data-image-id]').forEach(btn => {
                const imageId = btn.getAttribute('data-image-id');
                if (userFavorites.has(imageId)) {
                    btn.classList.add('favorited');
                }
            });
            
            showToast(`成功收藏了 ${successCount} 张图片`);
        }

        // 下载全部
        function downloadAll() {
            if (!confirm(`确定要下载这个图集的所有 ${currentImages.length} 张图片吗？`)) {
                return;
            }
            
            currentImages.forEach((image, index) => {
                setTimeout(() => {
                    downloadImage(image.path, image.filename);
                }, index * 500); // 每500ms下载一张，避免浏览器限制
            });
        }

        // 键盘事件
        document.addEventListener('keydown', function(e) {
            const modal = document.getElementById('imageModal');
            if (modal.style.display === 'block') {
                if (e.key === 'Escape') {
                    closeModal();
                } else if (e.key === 'ArrowLeft') {
                    prevImage();
                } else if (e.key === 'ArrowRight') {
                    nextImage();
                }
            }
        });

        // 点击模态框外部关闭
        document.getElementById('imageModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        // 无限滚动
        window.addEventListener('scroll', function() {
            if ((window.innerHeight + window.scrollY) >= document.body.offsetHeight - 1000) {
                if (hasMore && !loading) {
                    loadMoreImages();
                }
            }
        });

        // 触摸滑动支持（手机端）- 改进版
        let touchStartX = 0;
        let touchEndX = 0;
        let touchStartY = 0;
        let touchEndY = 0;

        document.getElementById('imageModal').addEventListener('touchstart', function(e) {
            touchStartX = e.changedTouches[0].screenX;
            touchStartY = e.changedTouches[0].screenY;
        }, { passive: true });

        document.getElementById('imageModal').addEventListener('touchend', function(e) {
            touchEndX = e.changedTouches[0].screenX;
            touchEndY = e.changedTouches[0].screenY;
            handleSwipe();
        }, { passive: true });

        function handleSwipe() {
            const swipeThreshold = 50;
            const diffX = touchStartX - touchEndX;
            const diffY = Math.abs(touchStartY - touchEndY);

            // 只有水平滑动距离大于垂直滑动距离时才触发切换
            if (Math.abs(diffX) > swipeThreshold && Math.abs(diffX) > diffY) {
                if (diffX > 0) {
                    // 向左滑动，显示下一张
                    nextImage();
                } else {
                    // 向右滑动，显示上一张
                    prevImage();
                }
            }
        }
    </script>
</body>
</html>'''

# 主页面HTML模板
MAIN_TEMPLATE = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>春色写真馆 - Spring Gallery</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 25%, #f0fff4 50%, #fff8dc 75%, #fdf5e6 100%);
            min-height: 100vh;
            color: #5d4e37;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            padding: 1rem 0;
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
            box-shadow: 0 2px 20px rgba(255, 182, 193, 0.3);
        }
        
        .nav {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
        }
        
        .logo {
            font-size: 2rem;
            font-weight: bold;
            background: linear-gradient(45deg, #ff69b4, #ffa500, #98fb98);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        .nav-links {
            display: flex;
            gap: 2rem;
            align-items: center;
        }
        
        .nav-link {
            color: #8b4513;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .nav-link:hover {
            background: linear-gradient(45deg, rgba(255, 182, 193, 0.3), rgba(255, 218, 185, 0.3));
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 182, 193, 0.4);
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 2px solid #ffb6c1;
            object-fit: cover;
        }
        
        .main-content {
            margin-top: 100px;
            padding: 2rem;
            max-width: 1400px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .hero-section {
            text-align: center;
            padding: 4rem 0;
            background: linear-gradient(135deg, rgba(255, 182, 193, 0.1), rgba(255, 218, 185, 0.1));
            border-radius: 30px;
            margin-bottom: 3rem;
            position: relative;
            overflow: hidden;
        }
        
        .hero-section::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 182, 193, 0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        
        .hero-title {
            font-size: 3.5rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #ff69b4, #ffa500, #98fb98, #dda0dd);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            position: relative;
            z-index: 1;
        }
        
        .hero-subtitle {
            font-size: 1.2rem;
            color: #8b4513;
            margin-bottom: 2rem;
            position: relative;
            z-index: 1;
        }
        
        .search-container {
            position: relative;
            max-width: 500px;
            margin: 0 auto 2rem;
            z-index: 1;
        }
        
        .search-input {
            width: 100%;
            padding: 1rem 3rem 1rem 1.5rem;
            border: 2px solid #ffb6c1;
            border-radius: 50px;
            font-size: 1rem;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .search-input:focus {
            outline: none;
            border-color: #ff69b4;
            box-shadow: 0 0 20px rgba(255, 105, 180, 0.3);
        }
        
        .search-btn {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            background: linear-gradient(45deg, #ff69b4, #ffa500);
            border: none;
            padding: 0.8rem;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .search-btn:hover {
            transform: translateY(-50%) scale(1.1);
            box-shadow: 0 4px 15px rgba(255, 105, 180, 0.4);
        }
        
        .view-toggle {
            text-align: center;
            margin-bottom: 2rem;
            position: relative;
            z-index: 1;
        }
        
        .toggle-btn {
            padding: 0.8rem 1.5rem;
            margin: 0 0.5rem;
            border: 2px solid #ffb6c1;
            background: rgba(255, 255, 255, 0.9);
            color: #8b4513;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
        }
        
        .toggle-btn.active, .toggle-btn:hover {
            background: linear-gradient(45deg, #ff69b4, #ffa500);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 105, 180, 0.4);
        }
        
        .categories {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .albums-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .category-card, .album-card {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            border: 1px solid rgba(255, 182, 193, 0.3);
        }
        
        .category-card::before, .album-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 182, 193, 0.2), transparent);
            transition: left 0.5s;
        }
        
        .category-card:hover::before, .album-card:hover::before {
            left: 100%;
        }
        
        .category-card:hover, .album-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(255, 182, 193, 0.3);
        }
        
        .category-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            display: block;
        }
        
        .album-cover {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 15px;
            margin-bottom: 1rem;
        }
        
        .category-title, .album-title {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
            color: #8b4513;
        }
        
        .category-count, .album-count {
            color: #cd853f;
            font-size: 0.9rem;
        }
        
        .filter-bar {
            background: rgba(255, 255, 255, 0.9);
            padding: 1rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            align-items: center;
        }
        
        .filter-select {
            padding: 0.5rem 1rem;
            border: 1px solid #ffb6c1;
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.9);
            color: #8b4513;
        }
        
        .loading {
            text-align: center;
            padding: 2rem;
            font-size: 1.2rem;
            color: #8b4513;
        }
        
        /* 优化的分页样式 - 遵循设计模式 */
        .pagination-container {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.5rem;
            margin: 2rem auto;
            padding: 1rem 1.5rem;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 25px;
            box-shadow: 0 4px 15px rgba(255, 182, 193, 0.2);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 182, 193, 0.3);
            width: fit-content;
            max-width: 90%;
            flex-wrap: wrap;
        }

        .page-btn {
            padding: 0.5rem 0.8rem;
            border: 1px solid #ffb6c1;
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.9);
            color: #8b4513;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            min-width: 40px;
            text-align: center;
            white-space: nowrap;
        }

        .page-btn:hover:not(:disabled):not(.active) {
            background: linear-gradient(45deg, #ff69b4, #ffa500);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 105, 180, 0.4);
        }

        .page-btn.active {
            background: linear-gradient(45deg, #ff69b4, #ffa500);
            color: white;
            border-color: #ff69b4;
            font-weight: bold;
        }

        .page-btn:disabled {
            opacity: 0.4;
            cursor: not-allowed;
            background: rgba(200, 200, 200, 0.3);
            color: #999;
            transform: none;
            box-shadow: none;
        }

        .page-ellipsis {
            padding: 0.6rem 0.4rem;
            color: #8b4513;
            font-weight: 600;
            font-size: 0.9rem;
            opacity: 0.7;
        }

        .page-nav-btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 20px;
            background: linear-gradient(45deg, #ff69b4, #ffa500);
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            white-space: nowrap;
        }

        .page-nav-btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 105, 180, 0.4);
        }

        .page-nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            background: rgba(200, 200, 200, 0.5);
        }

        /* 收藏标签样式 - 遵循设计模式 */
        .favorites-tabs {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 2rem;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 25px;
            padding: 1rem;
            box-shadow: 0 4px 15px rgba(255, 182, 193, 0.2);
            border: 1px solid rgba(255, 182, 193, 0.3);
        }

        .tab-btn {
            padding: 0.8rem 2rem;
            border: 1px solid #ffb6c1;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
            color: #8b4513;
            position: relative;
            overflow: hidden;
        }

        .tab-btn.active {
            background: linear-gradient(45deg, #ff69b4, #ffa500);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 105, 180, 0.4);
        }

        .tab-btn:hover:not(.active) {
            background: linear-gradient(45deg, rgba(255, 182, 193, 0.3), rgba(255, 218, 185, 0.3));
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 182, 193, 0.4);
        }

        /* 视图模式切换样式 - 遵循设计模式 */
        .view-mode-toggle {
            display: flex;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            padding: 0.3rem;
            border: 1px solid #ffb6c1;
            gap: 0.2rem;
        }

        .view-mode-btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            background: transparent;
            color: #8b4513;
            white-space: nowrap;
        }

        .view-mode-btn.active {
            background: linear-gradient(45deg, #ff69b4, #ffa500);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 105, 180, 0.4);
        }

        .view-mode-btn:hover:not(.active) {
            background: linear-gradient(45deg, rgba(255, 182, 193, 0.3), rgba(255, 218, 185, 0.3));
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 182, 193, 0.4);
        }

        @media (max-width: 768px) {
            .nav {
                flex-direction: column;
                gap: 1rem;
            }

            .hero-title {
                font-size: 2.5rem;
            }

            .categories, .albums-grid {
                grid-template-columns: 1fr;
            }

            .filter-bar {
                flex-direction: column;
                align-items: stretch;
            }

            .pagination-container {
                gap: 0.3rem;
                padding: 0.8rem 1rem;
                margin: 1.5rem auto;
                max-width: 95%;
                flex-wrap: wrap;
            }

            .page-btn, .page-nav-btn {
                padding: 0.4rem 0.6rem;
                font-size: 0.8rem;
                min-width: 35px;
            }

            .page-nav-btn {
                padding: 0.4rem 0.8rem;
            }

            .favorites-tabs {
                flex-direction: column;
                gap: 0.8rem;
                padding: 0.8rem;
            }

            .tab-btn {
                padding: 0.6rem 1.5rem;
                font-size: 0.9rem;
            }

            .view-mode-toggle {
                flex-direction: column;
                gap: 0.2rem;
            }

            .view-mode-btn {
                padding: 0.4rem 0.8rem;
                font-size: 0.8rem;
            }
        }
        
        .footer {
            background: linear-gradient(135deg, rgba(255, 182, 193, 0.2), rgba(255, 218, 185, 0.2));
            padding: 3rem 0;
            margin-top: 4rem;
            text-align: center;
            color: #8b4513;
        }
        
        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }
        
        .footer-links {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }
        
        .footer-link {
            color: #8b4513;
            text-decoration: none;
            transition: color 0.3s ease;
        }
        
        .footer-link:hover {
            color: #ff69b4;
        }
    </style>
</head>
<body>
    <header class="header">
        <nav class="nav">
            <a href="/" class="logo" style="text-decoration: none; color: inherit;">🌸 春色写真馆</a>
            <div class="nav-links">
                <a href="/" class="nav-link">首页</a>
                <a href="/gallery" class="nav-link">图库</a>
                <a href="/favorites" class="nav-link">收藏</a>
                {% if session.user_id %}
                    <div class="user-info">
                        <img src="{{ session.avatar or '/static/default_avatar.jpg' }}" alt="头像" class="avatar">
                        <span>{{ session.username }}</span>
                        <a href="/logout" class="nav-link">退出</a>
                    </div>
                {% else %}
                    <a href="/login" class="nav-link">登录</a>
                    <a href="/register" class="nav-link">注册</a>
                {% endif %}
            </div>
        </nav>
    </header>

    <main class="main-content">
        <section class="hero-section">
            <h1 class="hero-title">春色写真馆</h1>
            <p class="hero-subtitle">发现美的瞬间，感受春天的温暖</p>
            <div class="search-container">
                <input type="text" class="search-input" placeholder="搜索你喜欢的内容..." id="searchInput">
                <button class="search-btn" onclick="performSearch()">🔍</button>
            </div>
            <div class="view-toggle">
                <button class="toggle-btn active" onclick="switchView('categories')" id="categoriesBtn">分类浏览</button>
                <button class="toggle-btn" onclick="switchView('albums')" id="albumsBtn">图集浏览</button>
            </div>
        </section>

        <div class="filter-bar">
            <select class="filter-select" id="categoryFilter" onchange="filterContent()">
                <option value="">所有分类</option>
                <option value="korea">韩系</option>
                <option value="cosplay">角色扮演</option>
                <option value="japan">日系</option>
                <option value="gravure">写真</option>
                <option value="chinese">中式</option>
                <option value="thailand">泰式</option>
            </select>
            <select class="filter-select" id="sortFilter" onchange="filterContent()">
                <option value="newest">最新</option>
                <option value="popular">最受欢迎</option>
                <option value="count">图片最多</option>
            </select>
        </div>

        <section class="categories" id="categoriesSection">
            <div class="category-card" onclick="showCategory('korea')">
                <span class="category-icon">🌸</span>
                <h3 class="category-title">韩系风格</h3>
                <p class="category-count" id="korea-count">加载中...</p>
            </div>
            <div class="category-card" onclick="showCategory('cosplay')">
                <span class="category-icon">🎭</span>
                <h3 class="category-title">角色扮演</h3>
                <p class="category-count" id="cosplay-count">加载中...</p>
            </div>
            <div class="category-card" onclick="showCategory('japan')">
                <span class="category-icon">🌺</span>
                <h3 class="category-title">日系清新</h3>
                <p class="category-count" id="japan-count">加载中...</p>
            </div>
            <div class="category-card" onclick="showCategory('gravure')">
                <span class="category-icon">📸</span>
                <h3 class="category-title">专业写真</h3>
                <p class="category-count" id="gravure-count">加载中...</p>
            </div>
            <div class="category-card" onclick="showCategory('chinese')">
                <span class="category-icon">🏮</span>
                <h3 class="category-title">中式古典</h3>
                <p class="category-count" id="chinese-count">加载中...</p>
            </div>
            <div class="category-card" onclick="showCategory('thailand')">
                <span class="category-icon">🌴</span>
                <h3 class="category-title">泰式风情</h3>
                <p class="category-count" id="thailand-count">加载中...</p>
            </div>
        </section>

        <section class="albums-grid" id="albumsSection" style="display: none;">
            <div class="loading">正在加载图集...</div>
        </section>
    </main>

    <footer class="footer">
        <div class="footer-content">
            <div class="footer-links">
                <a href="/about" class="footer-link">关于我们</a>
                <a href="/contact" class="footer-link">联系方式</a>
                <a href="/privacy" class="footer-link">隐私政策</a>
                <a href="/terms" class="footer-link">使用条款</a>
            </div>
            <p>&copy; 2024 春色写真馆. 所有权利保留.</p>
        </div>
    </footer>

    <script>
        let currentView = 'categories';
        let currentAlbums = [];

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadCategoryCounts();
            
            // 检查URL参数
            const urlParams = new URLSearchParams(window.location.search);
            const category = urlParams.get('category');
            if (category) {
                document.getElementById('categoryFilter').value = category;
                switchView('albums');
                filterContent();
            }
        });

        // 加载分类统计
        async function loadCategoryCounts() {
            try {
                const response = await fetch('/api/stats');
                const stats = await response.json();
                
                Object.keys(stats).forEach(category => {
                    const element = document.getElementById(category + '-count');
                    if (element) {
                        element.textContent = `${stats[category].albums}个图集 · ${stats[category].images}张图片`;
                    }
                });
            } catch (error) {
                console.error('加载统计失败:', error);
            }
        }

        // 加载图集数据 - 支持分页
        let currentAlbumsPage = 1;
        let totalAlbumsPages = 1;

        async function loadAlbums(page = 1) {
            try {
                currentAlbumsPage = page;
                const category = document.getElementById('categoryFilter').value;
                const sort = document.getElementById('sortFilter').value;

                let url = '/api/albums';
                const params = new URLSearchParams();

                if (category) {
                    params.append('category', category);
                }
                if (sort) {
                    params.append('sort', sort);
                }
                params.append('page', page);
                params.append('limit', 16);

                url += '?' + params.toString();

                const response = await fetch(url);
                const data = await response.json();

                currentAlbums = data.albums;
                totalAlbumsPages = data.total_pages;
                displayAlbums();
                updateAlbumsPagination();

            } catch (error) {
                console.error('加载图集失败:', error);
                document.getElementById('albumsSection').innerHTML = '<div class="loading">加载失败，请刷新页面重试</div>';
            }
        }

        // 通用分页生成函数
        function generatePagination(currentPage, totalPages, onPageClick) {
            if (totalPages <= 1) return '';

            let paginationHTML = '<div class="pagination-container">';

            // 上一页按钮
            const prevDisabled = currentPage <= 1;
            paginationHTML += `<button class="page-nav-btn" onclick="${onPageClick}(${currentPage - 1})" ${prevDisabled ? 'disabled' : ''}>上一页</button>`;

            // 页码逻辑
            const delta = 2; // 当前页前后显示的页数
            const range = [];
            const rangeWithDots = [];

            // 计算显示的页码范围
            for (let i = Math.max(2, currentPage - delta); i <= Math.min(totalPages - 1, currentPage + delta); i++) {
                range.push(i);
            }

            // 添加第一页
            if (currentPage - delta > 2) {
                rangeWithDots.push(1, '...');
            } else {
                rangeWithDots.push(1);
            }

            // 添加中间页码
            rangeWithDots.push(...range);

            // 添加最后一页
            if (currentPage + delta < totalPages - 1) {
                rangeWithDots.push('...', totalPages);
            } else if (totalPages > 1) {
                rangeWithDots.push(totalPages);
            }

            // 生成页码按钮
            rangeWithDots.forEach(page => {
                if (page === '...') {
                    paginationHTML += '<span class="page-ellipsis">...</span>';
                } else {
                    const isActive = page === currentPage;
                    const activeClass = isActive ? 'active' : '';
                    paginationHTML += `<button class="page-btn ${activeClass}" onclick="${onPageClick}(${page})">${page}</button>`;
                }
            });

            // 下一页按钮
            const nextDisabled = currentPage >= totalPages;
            paginationHTML += `<button class="page-nav-btn" onclick="${onPageClick}(${currentPage + 1})" ${nextDisabled ? 'disabled' : ''}>下一页</button>`;

            paginationHTML += '</div>';
            return paginationHTML;
        }

        // 更新图集分页控件
        function updateAlbumsPagination() {
            const albumsSection = document.getElementById('albumsSection');

            // 移除现有分页
            const existingPagination = albumsSection.querySelector('.pagination-container');
            if (existingPagination) {
                existingPagination.remove();
            }

            // 添加新分页
            if (totalAlbumsPages > 1) {
                const paginationHTML = generatePagination(currentAlbumsPage, totalAlbumsPages, 'loadAlbums');
                albumsSection.insertAdjacentHTML('beforeend', paginationHTML);
            }
        }

        // 显示图集
        function displayAlbums() {
            const section = document.getElementById('albumsSection');
            
            if (currentAlbums.length === 0) {
                section.innerHTML = '<div class="loading">没有找到相关图集</div>';
                return;
            }

            section.innerHTML = currentAlbums.map(album => `
                <div class="album-card" onclick="openAlbum('${album.category}', '${album.album}')">
                    <img src="${album.cover_image}" alt="${album.album}" class="album-cover" loading="lazy" onerror="this.src='/static/placeholder.jpg'">
                    <h3 class="album-title">${album.album}</h3>
                    <p class="album-count">${getCategoryName(album.category)} · ${album.image_count}张图片</p>
                </div>
            `).join('');
        }

        // 获取分类中文名
        function getCategoryName(category) {
            const names = {
                'korea': '韩系',
                'cosplay': '角色扮演',
                'japan': '日系',
                'gravure': '写真',
                'chinese': '中式',
                'thailand': '泰式'
            };
            return names[category] || category;
        }

        // 切换视图
        function switchView(view) {
            currentView = view;
            
            // 更新按钮状态
            document.getElementById('categoriesBtn').classList.toggle('active', view === 'categories');
            document.getElementById('albumsBtn').classList.toggle('active', view === 'albums');
            
            // 显示/隐藏对应区域
            document.getElementById('categoriesSection').style.display = view === 'categories' ? 'grid' : 'none';
            document.getElementById('albumsSection').style.display = view === 'albums' ? 'grid' : 'none';
            
            // 如果切换到图集视图且还没加载数据，则加载
            if (view === 'albums' && currentAlbums.length === 0) {
                loadAlbums();
            }
        }

        // 过滤内容
        function filterContent() {
            if (currentView === 'albums') {
                currentAlbumsPage = 1; // 重置页码
                loadAlbums(1);
            }
        }

        // 搜索功能
        async function performSearch() {
            const query = document.getElementById('searchInput').value.toLowerCase().trim();

            if (!query) {
                return;
            }

            // 切换到图集视图进行搜索
            switchView('albums');

            try {
                const response = await fetch(`/api/albums?search=${encodeURIComponent(query)}&page=1&limit=16`);
                const data = await response.json();
                currentAlbums = data.albums || data; // 兼容新旧API格式
                totalAlbumsPages = data.total_pages || 1;
                currentAlbumsPage = 1;
                displayAlbums();
                updateAlbumsPagination();
            } catch (error) {
                console.error('搜索失败:', error);
                document.getElementById('albumsSection').innerHTML = '<div class="loading">搜索失败，请重试</div>';
            }
        }

        // 显示分类
        function showCategory(category) {
            document.getElementById('categoryFilter').value = category;
            switchView('albums');
            filterContent();
        }

        // 打开图集
        function openAlbum(category, album) {
            window.location.href = `/album/${category}/${encodeURIComponent(album)}`;
        }

        // 键盘事件
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && document.getElementById('searchInput') === document.activeElement) {
                performSearch();
            }
        });
    </script>
</body>
</html>'''

# 收藏页面模板
# BUG FIX: favorites.js logic updated to work with server-side pagination
FAVORITES_TEMPLATE = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的收藏 - 春色写真馆</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 25%, #f0fff4 50%, #fff8dc 75%, #fdf5e6 100%);
            min-height: 100vh;
            color: #5d4e37;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            padding: 1rem 0;
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
            box-shadow: 0 2px 20px rgba(255, 182, 193, 0.3);
        }
        
        .nav {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
        }
        
        .logo {
            font-size: 2rem;
            font-weight: bold;
            background: linear-gradient(45deg, #ff69b4, #ffa500, #98fb98);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        .nav-links {
            display: flex;
            gap: 2rem;
            align-items: center;
        }
        
        .nav-link {
            color: #8b4513;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .nav-link:hover, .nav-link.active {
            background: linear-gradient(45deg, rgba(255, 182, 193, 0.3), rgba(255, 218, 185, 0.3));
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 182, 193, 0.4);
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 2px solid #ffb6c1;
            object-fit: cover;
        }
        
        .main-content {
            margin-top: 100px;
            padding: 2rem;
            max-width: 1400px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .page-header {
            text-align: center;
            padding: 3rem 0;
            background: linear-gradient(135deg, rgba(255, 182, 193, 0.1), rgba(255, 218, 185, 0.1));
            border-radius: 30px;
            margin-bottom: 3rem;
            position: relative;
            overflow: hidden;
        }
        
        .page-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 182, 193, 0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        
        .page-title {
            font-size: 3rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #ff69b4, #ffa500, #98fb98, #dda0dd);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            position: relative;
            z-index: 1;
        }
        
        .page-subtitle {
            font-size: 1.2rem;
            color: #8b4513;
            position: relative;
            z-index: 1;
        }
        
        .favorites-stats {
            background: rgba(255, 255, 255, 0.9);
            padding: 2rem;
            border-radius: 20px;
            margin-bottom: 3rem;
            text-align: center;
            box-shadow: 0 4px 15px rgba(255, 182, 193, 0.2);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
            margin-top: 1rem;
        }
        
        .stat-item {
            padding: 1rem;
            background: linear-gradient(135deg, rgba(255, 182, 193, 0.1), rgba(255, 218, 185, 0.1));
            border-radius: 15px;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #ff69b4;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #8b4513;
            font-size: 0.9rem;
        }
        
        .filter-bar {
            background: rgba(255, 255, 255, 0.9);
            padding: 1rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            align-items: center;
            justify-content: space-between;
        }
        
        .filter-left {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            align-items: center;
        }


        
        .filter-select {
            padding: 0.5rem 1rem;
            border: 1px solid #ffb6c1;
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.9);
            color: #8b4513;
        }
        
        .clear-all-btn {
            padding: 0.5rem 1rem;
            background: linear-gradient(45deg, #ff6b6b, #ffa500);
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .clear-all-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
        }
        
        .gallery-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }
        
        .image-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            overflow: hidden;
            transition: all 0.3s ease;
            position: relative;
            box-shadow: 0 4px 15px rgba(255, 182, 193, 0.2);
        }
        
        .image-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(255, 182, 193, 0.4);
        }
        
        .image-card img {
            width: 100%;
            height: 200px;
            object-fit: cover;
            transition: transform 0.3s ease;
        }
        
        .image-card:hover img {
            transform: scale(1.05);
        }
        
        .image-info {
            padding: 1rem;
        }
        
        .image-title {
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: #8b4513;
            cursor: pointer;
        }
        
        .image-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.9rem;
            color: #cd853f;
            margin-bottom: 1rem;
        }
        
        .favorited-time {
            font-size: 0.8rem;
            color: #999;
            margin-bottom: 1rem;
        }
        
        .card-actions {
            display: flex;
            gap: 0.5rem;
        }
        
        .btn-small {
            padding: 0.4rem 0.8rem;
            border: none;
            border-radius: 15px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.3rem;
        }
        
        .btn-remove {
            background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
            color: white;
        }
        
        .btn-view {
            background: linear-gradient(45deg, #98fb98, #87ceeb);
            color: #2f4f4f;
        }
        
        .btn-small:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }
        
        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 20px;
            margin: 2rem 0;
            /* BUG FIX: Ensure it spans the whole grid width */
            grid-column: 1 / -1;
        }
        
        .empty-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
        
        .empty-title {
            font-size: 1.5rem;
            color: #8b4513;
            margin-bottom: 1rem;
        }
        
        .empty-text {
            color: #cd853f;
            margin-bottom: 2rem;
        }
        
        .btn-primary {
            padding: 1rem 2rem;
            background: linear-gradient(45deg, #ff69b4, #ffa500);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 105, 180, 0.4);
        }
        
        .loading {
            text-align: center;
            padding: 2rem;
            font-size: 1.2rem;
            color: #8b4513;
            /* BUG FIX: Ensure it spans the whole grid width */
            grid-column: 1 / -1;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-top: 3rem;
        }
        
        .page-btn {
            padding: 0.8rem 1.2rem;
            border: 1px solid #ffb6c1;
            background: rgba(255, 255, 255, 0.9);
            color: #8b4513;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .page-btn:hover, .page-btn.active {
            background: linear-gradient(45deg, #ff69b4, #ffa500);
            color: white;
            transform: translateY(-2px);
        }
        
        .page-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
            border-color: #ddd;
        }

        @media (max-width: 768px) {
            .nav {
                flex-direction: column;
                gap: 1rem;
            }
            
            .page-title {
                font-size: 2.5rem;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .gallery-grid {
                grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
                gap: 1rem;
            }
            
            .filter-bar {
                flex-direction: column;
                align-items: stretch;
            }
            
            .filter-left {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <nav class="nav">
            <a href="/" class="logo" style="text-decoration: none; color: inherit;">🌸 春色写真馆</a>
            <div class="nav-links">
                <a href="/" class="nav-link">首页</a>
                <a href="/gallery" class="nav-link">图库</a>
                <a href="/favorites" class="nav-link active">收藏</a>
                <div class="user-info">
                    <img src="{{ session.avatar or '/static/default_avatar.jpg' }}" alt="头像" class="avatar">
                    <span>{{ session.username }}</span>
                    <a href="/logout" class="nav-link">退出</a>
                </div>
            </div>
        </nav>
    </header>

    <main class="main-content">
        <section class="page-header">
            <h1 class="page-title">❤️ 我的收藏</h1>
            <p class="page-subtitle">珍藏美好瞬间，记录心动时刻</p>
        </section>

        <div class="favorites-stats">
            <h3 style="color: #8b4513; margin-bottom: 1rem;">收藏统计</h3>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number" id="totalFavorites">0</div>
                    <div class="stat-label">总收藏数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="categoriesCount">0</div>
                    <div class="stat-label">涉及分类</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="albumsCount">0</div>
                    <div class="stat-label">涉及图集</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="recentCount">0</div>
                    <div class="stat-label">本周新增</div>
                </div>
            </div>
        </div>

        <!-- 收藏类型选择标签 -->
        <div class="favorites-tabs">
            <button class="tab-btn active" onclick="switchFavoritesTab('albums')" data-tab="albums">
                📂 收藏图集
            </button>
            <button class="tab-btn" onclick="switchFavoritesTab('images')" data-tab="images">
                📸 收藏图片
            </button>
        </div>

        <!-- 图集收藏过滤栏 -->
        <div class="filter-bar" id="albumsFilterBar">
            <div class="filter-left">
                <span class="filter-label">收藏的图集</span>
            </div>
            <button class="clear-all-btn" onclick="clearAllAlbumFavorites()">🗑️ 清空图集收藏</button>
        </div>

        <!-- 图片收藏过滤栏 -->
        <div class="filter-bar" id="imagesFilterBar" style="display: none;">
            <div class="filter-left">
                <!-- 图片收藏模式切换 -->
                <div class="view-mode-toggle">
                    <button class="view-mode-btn active" onclick="switchImagesMode('all')" data-mode="all">
                        🖼️ 全部显示
                    </button>
                    <button class="view-mode-btn" onclick="switchImagesMode('grouped')" data-mode="grouped">
                        📁 按图集分组
                    </button>
                </div>
                <select class="filter-select" id="categoryFilter" onchange="filterFavorites()">
                    <option value="">所有分类</option>
                    <option value="korea">韩系</option>
                    <option value="cosplay">角色扮演</option>
                    <option value="japan">日系</option>
                    <option value="gravure">写真</option>
                    <option value="chinese">中式</option>
                    <option value="thailand">泰式</option>
                </select>
                <select class="filter-select" id="sortFilter" onchange="filterFavorites()">
                    <option value="newest">最新收藏</option>
                    <option value="oldest">最早收藏</option>
                </select>
            </div>
            <button class="clear-all-btn" onclick="clearAllFavorites()">🗑️ 清空图片收藏</button>
        </div>

        <section class="gallery-grid" id="favoritesGrid">
            <div class="loading">正在加载收藏内容...</div>
        </section>

        <div class="pagination" id="pagination"></div>

        <!-- 图片模态框 -->
        <div id="imageModal" class="modal" style="display: none;">
            <div class="modal-content">
                <span class="close" onclick="closeModal()">&times;</span>
                <button class="modal-nav prev" onclick="prevImage()">❮</button>
                <img id="modalImage" src="" alt="">
                <button class="modal-nav next" onclick="nextImage()">❯</button>
                <div class="modal-info">
                    <span id="modalImageInfo"></span>
                </div>
            </div>
        </div>
    </main>

    <!-- BUG FIX: 全面重写收藏页面的 JS 逻辑以支持后端分页和双视图模式 -->
    <script>
        let currentPage = 1;
        let itemsPerPage = 12;
        let totalFavorites = 0;
        let currentFavoritesTab = 'albums'; // 'albums' 或 'images'
        let currentImagesMode = 'all'; // 'all' 或 'grouped'
        let currentImages = []; // 当前显示的图片数组
        let currentModalIndex = 0; // 当前模态框显示的图片索引

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            switchFavoritesTab('albums'); // 默认显示图集收藏
            loadFavoritesStats();
        });

        // 切换收藏标签
        function switchFavoritesTab(tab) {
            currentFavoritesTab = tab;
            currentPage = 1;

            // 清除图集上下文
            window.currentFavoritedAlbum = null;

            // 更新标签按钮状态
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-tab="${tab}"]`).classList.add('active');

            // 显示/隐藏对应的过滤栏
            const albumsFilterBar = document.getElementById('albumsFilterBar');
            const imagesFilterBar = document.getElementById('imagesFilterBar');

            if (tab === 'albums') {
                albumsFilterBar.style.display = 'flex';
                imagesFilterBar.style.display = 'none';
                loadFavoritedAlbums();
            } else {
                albumsFilterBar.style.display = 'none';
                imagesFilterBar.style.display = 'flex';
                if (currentImagesMode === 'all') {
                    loadFavorites();
                } else {
                    loadFavoritesGrouped();
                }
            }
        }

        // 切换图片收藏模式
        function switchImagesMode(mode) {
            currentImagesMode = mode;
            currentPage = 1;

            // 清除图集上下文
            window.currentFavoritedAlbum = null;

            // 更新模式按钮状态
            document.querySelectorAll('.view-mode-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-mode="${mode}"]`).classList.add('active');

            // 加载对应数据
            if (mode === 'all') {
                loadFavorites();
            } else {
                loadFavoritesGrouped();
            }
        }

        // 加载收藏数据 (现在支持分页)
        async function loadFavorites(page = 1) {
            currentPage = page;
            const grid = document.getElementById('favoritesGrid');
            grid.innerHTML = '<div class="loading">正在加载收藏内容...</div>';

            try {
                const category = document.getElementById('categoryFilter').value;
                const sort = document.getElementById('sortFilter').value;
                
                const params = new URLSearchParams({
                    page: currentPage,
                    limit: itemsPerPage,
                    sort: sort
                });
                if (category) {
                    params.append('category', category);
                }

                const response = await fetch(`/api/favorites?${params.toString()}`);
                const data = await response.json();
                
                totalFavorites = data.total;
                displayFavorites(data.favorites);
                updatePagination();

            } catch (error) {
                console.error('加载收藏失败:', error);
                grid.innerHTML = '<div class="loading">加载失败，请刷新页面重试</div>';
            }
        }

        // 加载收藏统计
        async function loadFavoritesStats() {
            try {
                const response = await fetch('/api/favorites-stats');
                const stats = await response.json();
                
                document.getElementById('totalFavorites').textContent = stats.total || 0;
                document.getElementById('categoriesCount').textContent = stats.categories || 0;
                document.getElementById('albumsCount').textContent = stats.albums || 0;
                document.getElementById('recentCount').textContent = stats.recent || 0;
            } catch (error) {
                console.error('加载统计失败:', error);
            }
        }

        // 显示收藏 - 统一画廊视图 (重用相册画廊代码)
        function displayFavorites(favorites) {
            const grid = document.getElementById('favoritesGrid');

            if (!favorites || favorites.length === 0) {
                grid.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">💔</div>
                        <h3 class="empty-title">还没有收藏任何内容</h3>
                        <p class="empty-text">去发现一些美好的瞬间，添加到收藏吧！</p>
                        <a href="/" class="btn-primary">🌸 去探索</a>
                    </div>
                `;
                return;
            }

            // 为全部显示模式创建虚拟图集数据
            currentImages = favorites.map((favorite, index) => ({
                id: favorite.id,
                path: favorite.path,
                filename: favorite.filename,
                category: favorite.category,
                album: favorite.album
            }));

            // 使用与相册相同的画廊布局
            grid.className = 'gallery-grid';
            grid.innerHTML = favorites.map((favorite, index) => `
                <div class="image-card" data-image-id="${favorite.id}">
                    <img src="${favorite.path}" alt="${favorite.filename}" loading="lazy" onerror="this.src='/static/placeholder.jpg'">
                    <div class="image-overlay">
                        <div class="image-actions">
                            <button class="action-btn favorited"
                                    onclick="event.stopPropagation(); removeFavorite('${favorite.id}', event)"
                                    title="移除收藏">
                                💔
                            </button>
                            <button class="action-btn" onclick="event.stopPropagation(); downloadImage('${favorite.path}', '${favorite.filename}')" title="下载">
                                ⬇️
                            </button>
                            <button class="action-btn" onclick="event.stopPropagation(); openModal(${index})" title="查看">
                                👁️
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 获取分类中文名
        function getCategoryName(category) {
            const names = {
                'korea': '韩系',
                'cosplay': '角色扮演',
                'japan': '日系',
                'gravure': '写真',
                'chinese': '中式',
                'thailand': '泰式'
            };
            return names[category] || category;
        }

        // 更新分页 (现在基于总数计算)
        function updatePagination() {
            const totalPages = Math.ceil(totalFavorites / itemsPerPage);
            const pagination = document.getElementById('pagination');

            if (totalPages <= 1) {
                pagination.innerHTML = '';
                return;
            }

            const paginationHTML = generatePagination(currentPage, totalPages, 'changePage');
            pagination.innerHTML = paginationHTML;
        }

        // 切换页面 (现在会触发 API 请求)
        function changePage(page) {
            if (page < 1 || page > Math.ceil(totalFavorites / itemsPerPage)) {
                return;
            }

            // 检查当前是否在查看特定图集的收藏图片
            if (window.currentFavoritedAlbum) {
                loadFavoritedAlbumOnly(window.currentFavoritedAlbum.category, window.currentFavoritedAlbum.album, page);
            } else if (currentFavoritesTab === 'albums') {
                loadFavoritedAlbums(page);
            } else if (currentImagesMode === 'all') {
                loadFavorites(page);
            } else {
                loadFavoritesGrouped(page);
            }

            window.scrollTo({ top: 0, behavior: 'smooth' });
        }

        // 加载收藏的图集
        async function loadFavoritedAlbums(page = 1) {
            currentPage = page;
            const grid = document.getElementById('favoritesGrid');
            grid.innerHTML = '<div class="loading">正在加载收藏图集...</div>';

            try {
                const params = new URLSearchParams({
                    page: currentPage,
                    limit: itemsPerPage
                });

                const response = await fetch(`/api/favorited-albums?${params.toString()}`);
                const data = await response.json();

                totalFavorites = data.total;
                displayFavoritedAlbums(data.albums);
                updatePagination();

            } catch (error) {
                console.error('加载收藏图集失败:', error);
                grid.innerHTML = '<div class="loading">加载失败，请刷新页面重试</div>';
            }
        }

        // 显示收藏的图集
        function displayFavoritedAlbums(albums) {
            const grid = document.getElementById('favoritesGrid');

            if (!albums || albums.length === 0) {
                grid.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">💔</div>
                        <h3 class="empty-title">还没有收藏任何图集</h3>
                        <p class="empty-text">去发现一些美好的图集，添加到收藏吧！</p>
                        <a href="/" class="btn-primary">🌸 去探索</a>
                    </div>
                `;
                return;
            }

            // 使用相册卡片布局
            grid.className = 'albums-grid';
            grid.innerHTML = albums.map(album => `
                <div class="album-card" onclick="viewAlbum('${album.category}', '${album.album}')">
                    <img src="${album.cover_image}" alt="${album.album}" loading="lazy" onerror="this.src='/static/placeholder.jpg'">
                    <div class="album-info">
                        <h3 class="album-title">${album.album}</h3>
                        <div class="album-meta">
                            <span class="album-category">${getCategoryName(album.category)}</span>
                            <span class="album-count">${album.image_count} 张图片</span>
                        </div>
                        <div class="album-date">
                            收藏于 ${new Date(album.created_at).toLocaleDateString()}
                        </div>
                        <div class="album-actions">
                            <button class="btn-small btn-view" onclick="event.stopPropagation(); viewAlbum('${album.category}', '${album.album}')">
                                👁️ 查看图集
                            </button>
                            <button class="btn-small btn-unfavorite" onclick="event.stopPropagation(); unfavoriteAlbum('${album.category}', '${album.album}')">
                                💔 取消收藏
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 加载按图集分组的收藏图片
        async function loadFavoritesGrouped(page = 1) {
            currentPage = page;
            const grid = document.getElementById('favoritesGrid');
            grid.innerHTML = '<div class="loading">正在加载分组收藏...</div>';

            try {
                const params = new URLSearchParams({
                    page: currentPage,
                    limit: itemsPerPage
                });

                const response = await fetch(`/api/favorites-grouped?${params.toString()}`);
                const data = await response.json();

                totalFavorites = data.total;
                displayFavoritesGrouped(data.albums);
                updatePagination();

            } catch (error) {
                console.error('加载分组收藏失败:', error);
                grid.innerHTML = '<div class="loading">加载失败，请刷新页面重试</div>';
            }
        }

        // 显示按图集分组的收藏图片
        function displayFavoritesGrouped(albums) {
            const grid = document.getElementById('favoritesGrid');

            if (!albums || albums.length === 0) {
                grid.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">💔</div>
                        <h3 class="empty-title">还没有收藏任何图片</h3>
                        <p class="empty-text">去发现一些美好的瞬间，添加到收藏吧！</p>
                        <a href="/" class="btn-primary">🌸 去探索</a>
                    </div>
                `;
                return;
            }

            // 使用相册卡片布局
            grid.className = 'albums-grid';
            grid.innerHTML = albums.map(album => `
                <div class="album-card" onclick="viewFavoritedAlbumImages('${album.category}', '${album.album}')">
                    <img src="${album.cover_image}" alt="${album.album}" loading="lazy" onerror="this.src='/static/placeholder.jpg'">
                    <div class="album-info">
                        <h3 class="album-title">${album.album}</h3>
                        <div class="album-meta">
                            <span class="album-category">${getCategoryName(album.category)}</span>
                            <span class="album-count">收藏 ${album.favorite_count} 张</span>
                        </div>
                        <div class="album-date">
                            最近收藏于 ${new Date(album.latest_favorited).toLocaleDateString()}
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 过滤收藏 (重置到第一页并重新加载)
        function filterFavorites() {
            currentPage = 1;
            if (currentFavoritesTab === 'albums') {
                loadFavoritedAlbums(1);
            } else if (currentImagesMode === 'all') {
                loadFavorites(1);
            } else {
                loadFavoritesGrouped(1);
            }
        }

        // 查看图集
        function viewAlbum(category, album) {
            window.location.href = `/album/${category}/${encodeURIComponent(album)}`;
        }

        // 移除收藏
        async function removeFavorite(imageId, event) {
            event.stopPropagation();
            if (!confirm('确定要移除这个收藏吗？')) {
                return;
            }
            
            try {
                const response = await fetch('/api/favorite', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ image_id: imageId })
                });
                
                const result = await response.json();
                if (result.success) {
                    // 重新加载当前页的收藏列表和统计数据
                    loadFavorites(currentPage); 
                    loadFavoritesStats();
                } else {
                    alert(result.error || "移除失败");
                }
            } catch (error) {
                console.error('移除收藏失败:', error);
                alert("操作失败，请检查网络");
            }
        }

        // 取消收藏图集
        async function unfavoriteAlbum(category, album) {
            if (!confirm(`确定要取消收藏图集"${album}"吗？`)) {
                return;
            }

            try {
                const response = await fetch('/api/album-favorite', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        category: category,
                        album: album
                    })
                });

                const data = await response.json();
                if (data.success) {
                    showMessage(data.message, 'success');
                    loadFavoritedAlbums(currentPage);
                    loadFavoritesStats();
                } else {
                    showMessage(data.message || '操作失败', 'error');
                }
            } catch (error) {
                console.error('取消收藏图集失败:', error);
                showMessage('网络错误，请重试', 'error');
            }
        }

        // 查看收藏图集中的图片 - 仅显示收藏的图片
        function viewFavoritedAlbumImages(category, album) {
            // 切换到全部显示模式并加载该图集的收藏图片
            switchImagesMode('all');
            loadFavoritedAlbumOnly(category, album);
        }

        // 加载指定图集中的收藏图片
        async function loadFavoritedAlbumOnly(category, album, page = 1) {
            currentPage = page;
            // 设置当前查看的图集上下文
            window.currentFavoritedAlbum = { category, album };

            const grid = document.getElementById('favoritesGrid');
            grid.innerHTML = '<div class="loading">正在加载收藏图片...</div>';

            try {
                const params = new URLSearchParams({
                    page: currentPage,
                    limit: itemsPerPage
                });

                const response = await fetch(`/api/album-favorites-only/${category}/${album}?${params.toString()}`);
                const data = await response.json();

                totalFavorites = data.total;

                // 显示图集标题
                const categoryName = getCategoryName(category);
                grid.innerHTML = `
                    <div class="album-header" style="grid-column: 1 / -1; text-align: center; margin-bottom: 2rem;">
                        <h2 style="color: #8b4513; margin-bottom: 0.5rem;">📂 ${album}</h2>
                        <p style="color: #cd853f;">收藏的图片 (${data.total} 张)</p>
                        <button onclick="switchImagesMode('grouped')" style="margin-top: 1rem; padding: 0.5rem 1rem; background: linear-gradient(45deg, #ff69b4, #ffa500); color: white; border: none; border-radius: 20px; cursor: pointer;">
                            ← 返回分组视图
                        </button>
                    </div>
                `;

                if (data.images && data.images.length > 0) {
                    // 为收藏图片创建虚拟图集数据
                    currentImages = data.images.map((image, index) => ({
                        id: image.id,
                        path: image.path,
                        filename: image.filename,
                        category: category,
                        album: album
                    }));

                    // 使用画廊布局显示图片
                    grid.className = 'gallery-grid';
                    grid.innerHTML += data.images.map((image, index) => `
                        <div class="image-card" data-image-id="${image.id}">
                            <img src="${image.path}" alt="${image.filename}" loading="lazy" onerror="this.src='/static/placeholder.jpg'">
                            <div class="image-overlay">
                                <div class="image-actions">
                                    <button class="action-btn favorited"
                                            onclick="event.stopPropagation(); removeFavorite('${image.id}', event)"
                                            title="移除收藏">
                                        💔
                                    </button>
                                    <button class="action-btn" onclick="event.stopPropagation(); downloadImage('${image.path}', '${image.filename}')" title="下载">
                                        ⬇️
                                    </button>
                                    <button class="action-btn" onclick="event.stopPropagation(); openModal(${index})" title="查看">
                                        👁️
                                    </button>
                                </div>
                            </div>
                        </div>
                    `).join('');
                } else {
                    grid.innerHTML += `
                        <div class="empty-state" style="grid-column: 1 / -1;">
                            <div class="empty-icon">💔</div>
                            <h3 class="empty-title">该图集中没有收藏的图片</h3>
                            <p class="empty-text">去该图集中添加一些收藏吧！</p>
                        </div>
                    `;
                }

                updatePagination();

            } catch (error) {
                console.error('加载收藏图片失败:', error);
                grid.innerHTML = '<div class="loading">加载失败，请刷新页面重试</div>';
            }
        }

        // 清空所有图片收藏
        async function clearAllFavorites() {
            if (!confirm('确定要清空所有图片收藏吗？此操作不可恢复！')) {
                return;
            }

            try {
                const response = await fetch('/api/favorites/clear', { method: 'POST' });
                const result = await response.json();
                if (result.success) {
                    // 重新加载
                    if (currentImagesMode === 'all') {
                        loadFavorites(1);
                    } else {
                        loadFavoritesGrouped(1);
                    }
                    loadFavoritesStats();
                    alert('已清空所有图片收藏');
                }
            } catch (error) {
                console.error('清空收藏失败:', error);
            }
        }

        // 清空所有图集收藏
        async function clearAllAlbumFavorites() {
            if (!confirm('确定要清空所有图集收藏吗？此操作不可恢复！')) {
                return;
            }

            try {
                const response = await fetch('/api/clear-album-favorites', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const result = await response.json();
                if (result.success) {
                    loadFavoritedAlbums(1);
                    loadFavoritesStats();
                    alert('已清空所有图集收藏');
                } else {
                    alert('清空失败：' + result.message);
                }
            } catch (error) {
                console.error('清空图集收藏失败:', error);
                alert('网络错误，请重试');
            }
        }

        // 模态框功能
        function openModal(index) {
            if (index < 0 || index >= currentImages.length) {
                console.error('Invalid image index:', index);
                return;
            }
            currentModalIndex = index;
            updateModal();
            const modal = document.getElementById('imageModal');
            if (modal) {
                modal.style.display = 'block';
                document.body.style.overflow = 'hidden';
            }
        }

        function closeModal() {
            const modal = document.getElementById('imageModal');
            if (modal) {
                modal.style.display = 'none';
                document.body.style.overflow = 'auto';
            }
        }

        function updateModal() {
            if (currentImages.length === 0) return;

            const image = currentImages[currentModalIndex];
            const modalImage = document.getElementById('modalImage');
            const modalInfo = document.getElementById('modalImageInfo');

            if (modalImage && modalInfo) {
                modalImage.src = image.path;
                modalImage.alt = image.filename;
                modalInfo.textContent = `${image.filename} (${currentModalIndex + 1}/${currentImages.length})`;
            }
        }

        function prevImage() {
            if (currentImages.length === 0) return;
            currentModalIndex = (currentModalIndex - 1 + currentImages.length) % currentImages.length;
            updateModal();
        }

        function nextImage() {
            if (currentImages.length === 0) return;
            currentModalIndex = (currentModalIndex + 1) % currentImages.length;
            updateModal();
        }

        // 键盘导航
        document.addEventListener('keydown', function(e) {
            const modal = document.getElementById('imageModal');
            if (modal && modal.style.display === 'block') {
                if (e.key === 'Escape') {
                    closeModal();
                } else if (e.key === 'ArrowLeft') {
                    prevImage();
                } else if (e.key === 'ArrowRight') {
                    nextImage();
                }
            }
        });

        // 下载图片功能
        function downloadImage(imagePath, filename) {
            const link = document.createElement('a');
            link.href = imagePath;
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    </script>
</body>
</html>'''

# 登录页面模板
LOGIN_TEMPLATE = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 春色写真馆</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 25%, #f0fff4 50%, #fff8dc 75%, #fdf5e6 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #5d4e37;
        }
        
        .login-container {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            padding: 3rem;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(255, 182, 193, 0.3);
            width: 100%;
            max-width: 400px;
            position: relative;
            overflow: hidden;
        }
        
        .login-container::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 182, 193, 0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        
        .login-title {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 2rem;
            background: linear-gradient(45deg, #ff69b4, #ffa500, #98fb98);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            position: relative;
            z-index: 1;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
            position: relative;
            z-index: 1;
        }
        
        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            color: #8b4513;
            font-weight: bold;
        }
        
        .form-input {
            width: 100%;
            padding: 1rem;
            border: 2px solid #ffb6c1;
            border-radius: 10px;
            font-size: 1rem;
            background: rgba(255, 255, 255, 0.8);
            transition: all 0.3s ease;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #ff69b4;
            box-shadow: 0 0 15px rgba(255, 105, 180, 0.3);
        }
        
        .btn-login {
            width: 100%;
            padding: 1rem;
            background: linear-gradient(45deg, #ff69b4, #ffa500);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            z-index: 1;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 105, 180, 0.4);
        }
        
        .login-links {
            text-align: center;
            margin-top: 2rem;
            position: relative;
            z-index: 1;
        }
        
        .login-link {
            color: #8b4513;
            text-decoration: none;
            margin: 0 1rem;
            transition: color 0.3s ease;
        }
        
        .login-link:hover {
            color: #ff69b4;
        }
        
        .error-message {
            background: rgba(255, 0, 0, 0.1);
            color: #d32f2f;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .success-message {
            background: rgba(0, 255, 0, 0.1);
            color: #2e7d32;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1rem;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <h1 class="login-title"><a href="/" style="text-decoration: none; color: inherit;">🌸 登录</a></h1>
        
        {% if error %}
        <div class="error-message">{{ error }}</div>
        {% endif %}
        
        {% if success %}
        <div class="success-message">{{ success }}</div>
        {% endif %}
        
        <form method="POST">
            <div class="form-group">
                <label class="form-label" for="username">用户名或邮箱</label>
                <input type="text" class="form-input" id="username" name="username" required>
            </div>
            
            <div class="form-group">
                <label class="form-label" for="password">密码</label>
                <input type="password" class="form-input" id="password" name="password" required>
            </div>
            
            <button type="submit" class="btn-login">登录</button>
        </form>
        
        <div class="login-links">
            <a href="/register" class="login-link">注册账号</a>
            <a href="/" class="login-link">返回首页</a>
        </div>
    </div>
</body>
</html>'''

# 注册页面模板
REGISTER_TEMPLATE = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注册 - 春色写真馆</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 25%, #f0fff4 50%, #fff8dc 75%, #fdf5e6 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #5d4e37;
        }
        
        .register-container {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            padding: 3rem;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(255, 182, 193, 0.3);
            width: 100%;
            max-width: 400px;
            position: relative;
            overflow: hidden;
        }
        
        .register-container::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 182, 193, 0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        
        .register-title {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 2rem;
            background: linear-gradient(45deg, #ff69b4, #ffa500, #98fb98);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            position: relative;
            z-index: 1;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
            position: relative;
            z-index: 1;
        }
        
        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            color: #8b4513;
            font-weight: bold;
        }
        
        .form-input {
            width: 100%;
            padding: 1rem;
            border: 2px solid #ffb6c1;
            border-radius: 10px;
            font-size: 1rem;
            background: rgba(255, 255, 255, 0.8);
            transition: all 0.3s ease;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #ff69b4;
            box-shadow: 0 0 15px rgba(255, 105, 180, 0.3);
        }
        
        .btn-register {
            width: 100%;
            padding: 1rem;
            background: linear-gradient(45deg, #ff69b4, #ffa500);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            z-index: 1;
        }
        
        .btn-register:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 105, 180, 0.4);
        }
        
        .register-links {
            text-align: center;
            margin-top: 2rem;
            position: relative;
            z-index: 1;
        }
        
        .register-link {
            color: #8b4513;
            text-decoration: none;
            margin: 0 1rem;
            transition: color 0.3s ease;
        }
        
        .register-link:hover {
            color: #ff69b4;
        }
        
        .error-message {
            background: rgba(255, 0, 0, 0.1);
            color: #d32f2f;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .success-message {
            background: rgba(0, 255, 0, 0.1);
            color: #2e7d32;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1rem;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="register-container">
        <h1 class="register-title"><a href="/" style="text-decoration: none; color: inherit;">🌸 注册</a></h1>
        
        {% if error %}
        <div class="error-message">{{ error }}</div>
        {% endif %}
        
        {% if success %}
        <div class="success-message">{{ success }}</div>
        {% endif %}
        
        <form method="POST">
            <div class="form-group">
                <label class="form-label" for="username">用户名</label>
                <input type="text" class="form-input" id="username" name="username" required>
            </div>
            
            <div class="form-group">
                <label class="form-label" for="email">邮箱</label>
                <input type="email" class="form-input" id="email" name="email" required>
            </div>
            
            <div class="form-group">
                <label class="form-label" for="password">密码</label>
                <input type="password" class="form-input" id="password" name="password" required>
            </div>
            
            <div class="form-group">
                <label class="form-label" for="confirm_password">确认密码</label>
                <input type="password" class="form-input" id="confirm_password" name="confirm_password" required>
            </div>
            
            <button type="submit" class="btn-register">注册</button>
        </form>
        
        <div class="register-links">
            <a href="/login" class="register-link">已有账号？登录</a>
            <a href="/" class="register-link">返回首页</a>
        </div>
    </div>
</body>
</html>'''

# BUG FIX: 新增footer页面模板，内容符合中国法律要求
ABOUT_TEMPLATE = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>关于我们 - 春色写真馆</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 25%, #f0fff4 50%, #fff8dc 75%, #fdf5e6 100%);
            min-height: 100vh;
            color: #5d4e37;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
        }
        .header {
            text-align: center;
            margin-bottom: 3rem;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
        }
        .content {
            background: rgba(255, 255, 255, 0.9);
            padding: 2rem;
            border-radius: 20px;
            margin-bottom: 2rem;
        }
        h1 { color: #8b4513; margin-bottom: 1rem; }
        h2 { color: #cd853f; margin: 2rem 0 1rem 0; }
        .back-link {
            display: inline-block;
            padding: 0.8rem 1.5rem;
            background: linear-gradient(45deg, #ff69b4, #ffa500);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            transition: all 0.3s ease;
        }
        .back-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 105, 180, 0.4);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌸 关于春色写真馆</h1>
            <p>致力于提供优质的图片浏览和管理服务</p>
        </div>

        <div class="content">
            <h2>软件简介</h2>
            <p>春色写真馆是一个<strong>仅供本地使用</strong>的图片管理和浏览软件，专为个人用户在本地环境中管理和浏览图片而设计。</p>

            <h2>重要声明</h2>
            <div style="background: rgba(255, 99, 71, 0.1); padding: 1rem; border-radius: 10px; border-left: 4px solid #ff6347; margin: 1rem 0;">
                <strong>⚠️ 本软件仅限本地使用</strong>
                <ul>
                    <li>本软件设计用于个人本地环境，严禁部署到互联网服务器</li>
                    <li>禁止将本软件用于任何形式的网络服务或公开访问</li>
                    <li>用户需自行确保使用符合当地法律法规</li>
                </ul>
            </div>

            <h2>软件特色</h2>
            <ul>
                <li>🎨 精美的用户界面设计</li>
                <li>📸 多分类图片管理系统</li>
                <li>🔍 智能搜索和筛选功能</li>
                <li>❤️ 个性化收藏管理</li>
                <li>📱 响应式设计</li>
                <li>🔒 本地数据存储</li>
            </ul>

            <h2>开发者免责声明</h2>
            <div style="background: rgba(255, 182, 193, 0.1); padding: 1rem; border-radius: 10px; border-left: 4px solid #ff69b4; margin: 1rem 0;">
                <ul>
                    <li>开发者不对软件的任何误用承担责任</li>
                    <li>用户需自行承担使用风险和法律责任</li>
                    <li>开发者不提供任何形式的担保或保证</li>
                    <li>禁止商业使用或未经授权的分发</li>
                </ul>
            </div>

            <h2>使用限制</h2>
            <p>本软件仅供学习和个人使用，用户有责任确保其使用方式符合所在地区的法律法规。</p>
        </div>

        <div style="text-align: center;">
            <a href="/" class="back-link">🏠 返回首页</a>
        </div>
    </div>
</body>
</html>'''

CONTACT_TEMPLATE = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>联系方式 - 春色写真馆</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 25%, #f0fff4 50%, #fff8dc 75%, #fdf5e6 100%);
            min-height: 100vh;
            color: #5d4e37;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
        }
        .header {
            text-align: center;
            margin-bottom: 3rem;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
        }
        .content {
            background: rgba(255, 255, 255, 0.9);
            padding: 2rem;
            border-radius: 20px;
            margin-bottom: 2rem;
        }
        h1 { color: #8b4513; margin-bottom: 1rem; }
        h2 { color: #cd853f; margin: 2rem 0 1rem 0; }
        .contact-item {
            background: rgba(255, 182, 193, 0.1);
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 10px;
            border-left: 4px solid #ff69b4;
        }
        .back-link {
            display: inline-block;
            padding: 0.8rem 1.5rem;
            background: linear-gradient(45deg, #ff69b4, #ffa500);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            transition: all 0.3s ease;
        }
        .back-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 105, 180, 0.4);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📞 联系方式</h1>
            <p>我们随时为您提供帮助和支持</p>
        </div>

        <div class="content">
            <div style="background: rgba(255, 99, 71, 0.1); padding: 1rem; border-radius: 10px; border-left: 4px solid #ff6347; margin: 1rem 0;">
                <strong>⚠️ 重要提醒</strong><br>
                本软件为开源项目，仅供本地使用。开发者不提供商业支持服务。
            </div>

            <h2>开源项目信息</h2>
            <div class="contact-item">
                <strong>📂 项目性质：</strong><br>
                开源软件项目，仅供学习和个人使用<br>
                <small>不提供商业技术支持</small>
            </div>

            <h2>社区支持</h2>
            <div class="contact-item">
                <strong>💬 社区讨论：</strong><br>
                建议通过开源社区平台寻求帮助<br>
                <small>用户可自行交流使用经验</small>
            </div>

            <h2>问题反馈</h2>
            <div class="contact-item">
                <strong>🐛 Bug报告：</strong><br>
                可通过项目仓库提交问题反馈<br>
                <small>开发者会在有时间时查看</small>
            </div>

            <h2>免责声明</h2>
            <div style="background: rgba(255, 182, 193, 0.1); padding: 1rem; border-radius: 10px; border-left: 4px solid #ff69b4; margin: 1rem 0;">
                <ul>
                    <li>开发者不承担任何技术支持义务</li>
                    <li>软件按"现状"提供，无任何保证</li>
                    <li>用户自行承担使用风险</li>
                    <li>禁止用于商业用途</li>
                </ul>
            </div>

            <h2>使用建议</h2>
            <ul>
                <li>仔细阅读使用说明和注意事项</li>
                <li>确保在合法合规的环境下使用</li>
                <li>定期备份重要数据</li>
                <li>遇到问题可尝试重启软件</li>
            </ul>
        </div>

        <div style="text-align: center;">
            <a href="/" class="back-link">🏠 返回首页</a>
        </div>
    </div>
</body>
</html>'''

# 路由定义
@app.route('/')
def index():
    if not scan_progress['completed']:
        return render_template_string(STARTUP_TEMPLATE)
    return render_template_string(MAIN_TEMPLATE)

@app.route('/startup')
def startup():
    return render_template_string(STARTUP_TEMPLATE)

@app.route('/api/skip-scan', methods=['POST'])
def api_skip_scan():
    """BUG FIX: 跳过扫描功能，直接标记扫描完成"""
    global scan_progress
    scan_progress['completed'] = True
    scan_progress['status'] = '已跳过扫描'
    return jsonify({'success': True, 'message': '已跳过扫描'})

@app.route('/api/album-favorite', methods=['POST'])
@login_required
def api_toggle_album_favorite():
    """切换图集收藏状态"""
    user_id = session['user_id']
    data = request.get_json()
    category = data.get('category')
    album = data.get('album')

    if not category or not album:
        return jsonify({'success': False, 'message': '参数不完整'})

    conn = sqlite3.connect('gallery.db')
    c = conn.cursor()

    # 检查是否已收藏
    c.execute('SELECT id FROM album_favorites WHERE user_id = ? AND category = ? AND album = ?',
              (user_id, category, album))
    existing = c.fetchone()

    if existing:
        # 取消收藏
        c.execute('DELETE FROM album_favorites WHERE user_id = ? AND category = ? AND album = ?',
                  (user_id, category, album))
        is_favorited = False
        message = '已取消收藏图集'
    else:
        # 添加收藏
        c.execute('INSERT INTO album_favorites (user_id, category, album) VALUES (?, ?, ?)',
                  (user_id, category, album))
        is_favorited = True
        message = '已收藏图集'

    conn.commit()
    conn.close()

    return jsonify({
        'success': True,
        'is_favorited': is_favorited,
        'message': message
    })

@app.route('/api/album-favorite-status')
@login_required
def api_album_favorite_status():
    """检查图集收藏状态"""
    user_id = session['user_id']
    category = request.args.get('category')
    album = request.args.get('album')

    if not category or not album:
        return jsonify({'is_favorited': False})

    conn = sqlite3.connect('gallery.db')
    c = conn.cursor()

    c.execute('SELECT id FROM album_favorites WHERE user_id = ? AND category = ? AND album = ?',
              (user_id, category, album))
    is_favorited = c.fetchone() is not None

    conn.close()

    return jsonify({'is_favorited': is_favorited})

@app.route('/api/favorited-albums')
@login_required
def api_favorited_albums():
    """获取用户收藏的图集"""
    user_id = session['user_id']
    page = int(request.args.get('page', 1))
    limit = int(request.args.get('limit', 12))
    offset = (page - 1) * limit

    conn = sqlite3.connect('gallery.db')
    c = conn.cursor()

    # 获取收藏的图集信息
    c.execute('''SELECT af.category, af.album, af.created_at,
                        COUNT(i.id) as image_count,
                        MIN(i.path) as cover_image
                 FROM album_favorites af
                 LEFT JOIN images i ON af.category = i.category AND af.album = i.album
                 WHERE af.user_id = ?
                 GROUP BY af.category, af.album
                 ORDER BY af.created_at DESC
                 LIMIT ? OFFSET ?''', (user_id, limit, offset))

    results = c.fetchall()

    # 获取总数
    c.execute('SELECT COUNT(*) FROM album_favorites WHERE user_id = ?', (user_id,))
    total_count = c.fetchone()[0]

    conn.close()

    albums = []
    for row in results:
        albums.append({
            'category': row[0],
            'album': row[1],
            'created_at': row[2],
            'image_count': row[3],
            'cover_image': row[4] or '/static/placeholder.jpg'
        })

    return jsonify({
        'albums': albums,
        'total': total_count,
        'page': page,
        'limit': limit,
        'total_pages': (total_count + limit - 1) // limit
    })

@app.route('/api/favorites-grouped')
@login_required
def api_favorites_grouped():
    """获取按图集分组的收藏图片"""
    user_id = session['user_id']
    page = int(request.args.get('page', 1))
    limit = int(request.args.get('limit', 12))
    offset = (page - 1) * limit

    conn = sqlite3.connect('gallery.db')
    c = conn.cursor()

    # 获取收藏图片按图集分组
    c.execute('''SELECT i.category, i.album, COUNT(f.id) as favorite_count,
                        MIN(i.path) as cover_image, MAX(f.created_at) as latest_favorited
                 FROM favorites f
                 JOIN images i ON f.image_id = i.id
                 WHERE f.user_id = ?
                 GROUP BY i.category, i.album
                 ORDER BY latest_favorited DESC
                 LIMIT ? OFFSET ?''', (user_id, limit, offset))

    results = c.fetchall()

    # 获取总数
    c.execute('''SELECT COUNT(*)
                 FROM (SELECT DISTINCT i.category, i.album
                       FROM favorites f
                       JOIN images i ON f.image_id = i.id
                       WHERE f.user_id = ?)''', (user_id,))
    total_count = c.fetchone()[0]

    conn.close()

    albums = []
    for row in results:
        albums.append({
            'category': row[0],
            'album': row[1],
            'favorite_count': row[2],
            'cover_image': row[3],
            'latest_favorited': row[4]
        })

    return jsonify({
        'albums': albums,
        'total': total_count,
        'page': page,
        'limit': limit,
        'total_pages': (total_count + limit - 1) // limit
    })

@app.route('/api/clear-album-favorites', methods=['POST'])
@login_required
def api_clear_album_favorites():
    """清空用户的所有图集收藏"""
    user_id = session['user_id']

    conn = sqlite3.connect('gallery.db')
    c = conn.cursor()

    c.execute('DELETE FROM album_favorites WHERE user_id = ?', (user_id,))
    deleted_count = c.rowcount

    conn.commit()
    conn.close()

    return jsonify({
        'success': True,
        'message': f'已清空 {deleted_count} 个图集收藏'
    })

@app.route('/api/album-favorites-only/<category>/<album>')
@login_required
def api_album_favorites_only(category, album):
    """获取指定图集中用户收藏的图片"""
    user_id = session['user_id']
    page = int(request.args.get('page', 1))
    limit = int(request.args.get('limit', 20))
    offset = (page - 1) * limit

    conn = sqlite3.connect('gallery.db')
    c = conn.cursor()

    # 获取该图集中用户收藏的图片
    c.execute('''SELECT i.id, i.filename, i.path, i.full_path, i.file_size, f.created_at
                 FROM favorites f
                 JOIN images i ON f.image_id = i.id
                 WHERE f.user_id = ? AND i.category = ? AND i.album = ?
                 ORDER BY f.created_at DESC
                 LIMIT ? OFFSET ?''',
              (user_id, category, album, limit, offset))

    results = c.fetchall()

    # 获取总数
    c.execute('''SELECT COUNT(*)
                 FROM favorites f
                 JOIN images i ON f.image_id = i.id
                 WHERE f.user_id = ? AND i.category = ? AND i.album = ?''',
              (user_id, category, album))
    total_count = c.fetchone()[0]

    conn.close()

    images = []
    for row in results:
        images.append({
            'id': row[0],
            'filename': row[1],
            'path': row[2],
            'full_path': row[3],
            'file_size': row[4],
            'favorited_at': row[5]
        })

    return jsonify({
        'images': images,
        'total': total_count,
        'page': page,
        'limit': limit,
        'total_pages': (total_count + limit - 1) // limit if total_count > 0 else 0
    })

@app.route('/album/<category>/<album>')
def album_detail(category, album):
    category_name = get_category_name(category)
    return render_template_string(ALBUM_TEMPLATE,
                                 category=category,
                                 album=album,
                                 category_name=category_name)

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        conn = sqlite3.connect('gallery.db')
        c = conn.cursor()
        c.execute('SELECT id, username, password_hash, avatar FROM users WHERE username = ? OR email = ?',
                  (username, username))
        user = c.fetchone()
        conn.close()
        
        if user and check_password_hash(user[2], password):
            session['user_id'] = user[0]
            session['username'] = user[1]
            session['avatar'] = user[3]
            return redirect(url_for('index'))
        else:
            return render_template_string(LOGIN_TEMPLATE, error='用户名或密码错误')
    
    return render_template_string(LOGIN_TEMPLATE)

@app.route('/register', methods=['GET', 'POST'])
def register():
    if request.method == 'POST':
        username = request.form['username']
        email = request.form['email']
        password = request.form['password']
        confirm_password = request.form['confirm_password']

        if password != confirm_password:
            return render_template_string(REGISTER_TEMPLATE, error='两次输入的密码不一致')

        if len(password) < 6:
            return render_template_string(REGISTER_TEMPLATE, error='密码长度至少6位')

        conn = sqlite3.connect('gallery.db')
        c = conn.cursor()

        # 检查用户名和邮箱是否已存在
        c.execute('SELECT id FROM users WHERE username = ? OR email = ?', (username, email))
        if c.fetchone():
            conn.close()
            return render_template_string(REGISTER_TEMPLATE, error='用户名或邮箱已存在')

        # 创建新用户
        password_hash = generate_password_hash(password)
        c.execute('INSERT INTO users (username, email, password_hash) VALUES (?, ?, ?)',
                 (username, email, password_hash))
        user_id = c.lastrowid
        conn.commit()
        conn.close()

        # BUG FIX: 自动登录新注册的用户并重定向到主页
        session['user_id'] = user_id
        session['username'] = username
        session['avatar'] = '/static/default_avatar.jpg'
        return redirect(url_for('index'))

    return render_template_string(REGISTER_TEMPLATE)

@app.route('/logout')
def logout():
    session.clear()
    return redirect(url_for('index'))

@app.route('/images/<path:filename>')
def serve_image(filename):
    # BUG FIX: 处理URL编码问题，支持中文和特殊字符
    import urllib.parse

    # 解码URL编码的文件名
    try:
        decoded_filename = urllib.parse.unquote(filename, encoding='utf-8')
    except Exception as e:
        print(f"URL解码失败: {e}")
        decoded_filename = filename

    # 增加一层目录检查，防止恶意路径
    safe_path = os.path.normpath(os.path.join('downloaded', decoded_filename))
    if not safe_path.startswith('downloaded' + os.sep):
        return "Not Found", 404

    # 检查文件是否存在
    if not os.path.exists(safe_path):
        # 尝试原始文件名（未解码）
        original_safe_path = os.path.normpath(os.path.join('downloaded', filename))
        if os.path.exists(original_safe_path):
            return send_from_directory('downloaded', filename)

        # BUG FIX: 尝试不同的编码方式
        try:
            # 尝试GBK编码（中文Windows系统常用）
            gbk_decoded = urllib.parse.unquote(filename, encoding='gbk')
            gbk_safe_path = os.path.normpath(os.path.join('downloaded', gbk_decoded))
            if os.path.exists(gbk_safe_path) and gbk_safe_path.startswith('downloaded' + os.sep):
                return send_from_directory('downloaded', gbk_decoded)
        except:
            pass

        return "Not Found", 404

    return send_from_directory('downloaded', decoded_filename)

@app.route('/api/scan-progress')
def api_scan_progress():
    return jsonify(scan_progress)

@app.route('/api/images')
def api_images():
    category = request.args.get('category')
    search = request.args.get('search')
    sort = request.args.get('sort', 'newest')
    page = int(request.args.get('page', 1))
    limit = int(request.args.get('limit', 50))
    offset = (page - 1) * limit
    
    images = get_images_from_db(category=category, search=search, limit=limit, offset=offset)
    
    # 简单的排序逻辑
    if sort == 'popular':
        random.shuffle(images)
    elif sort == 'rating':
        random.shuffle(images)
    
    return jsonify(images)

@app.route('/api/albums')
def api_albums():
    category = request.args.get('category')
    search = request.args.get('search')
    sort = request.args.get('sort', 'newest')
    page = int(request.args.get('page', 1))
    limit = int(request.args.get('limit', 16))  # 16 albums per page
    offset = (page - 1) * limit

    albums = get_albums_from_db(category=category)

    # 搜索过滤
    if search:
        albums = [album for album in albums if search.lower() in album['album'].lower()]

    # 排序
    if sort == 'count':
        albums.sort(key=lambda x: x['image_count'], reverse=True)
    elif sort == 'popular':
        random.shuffle(albums)

    # 分页
    total_albums = len(albums)
    paginated_albums = albums[offset:offset + limit]

    return jsonify({
        'albums': paginated_albums,
        'total': total_albums,
        'page': page,
        'limit': limit,
        'total_pages': (total_albums + limit - 1) // limit
    })

@app.route('/api/album-images')
def api_album_images():
    category = request.args.get('category')
    album = request.args.get('album')
    page = int(request.args.get('page', 1))
    limit = int(request.args.get('limit', 20))
    offset = (page - 1) * limit
    
    if not category or not album:
        return jsonify({'error': 'Missing parameters'}), 400
    
    images = get_images_from_db(category=category, album=album, limit=limit, offset=offset)
    
    # 获取总数
    conn = sqlite3.connect('gallery.db')
    c = conn.cursor()
    c.execute('SELECT COUNT(*) FROM images WHERE category = ? AND album = ?', (category, album))
    total = c.fetchone()[0]
    conn.close()
    
    return jsonify({
        'images': images,
        'total': total,
        'page': page,
        'limit': limit
    })

@app.route('/api/stats')
def api_stats():
    conn = sqlite3.connect('gallery.db')
    c = conn.cursor()
    
    stats = {}
    categories = ['korea', 'cosplay', 'japan', 'gravure', 'chinese', 'thailand']
    
    for category in categories:
        c.execute('SELECT COUNT(*) FROM images WHERE category = ?', (category,))
        image_count = c.fetchone()[0]
        
        c.execute('SELECT COUNT(DISTINCT album) FROM images WHERE category = ?', (category,))
        album_count = c.fetchone()[0]
        
        stats[category] = {
            'albums': album_count,
            'images': image_count
        }
    
    conn.close()
    return jsonify(stats)

@app.route('/api/user-favorites')
@login_required
def api_user_favorites():
    """获取用户收藏的图片ID列表"""
    user_id = session['user_id']
    
    conn = sqlite3.connect('gallery.db')
    c = conn.cursor()
    c.execute('SELECT image_id FROM favorites WHERE user_id = ?', (user_id,))
    favorites = [{'image_id': row[0]} for row in c.fetchall()]
    conn.close()
    
    return jsonify(favorites)

@app.route('/api/favorite', methods=['POST'])
@login_required
def api_favorite():
    data = request.get_json()
    image_id = data.get('image_id')
    user_id = session['user_id']
    
    if not image_id:
        return jsonify({'success': False, 'error': 'Image ID is required'}), 400

    conn = sqlite3.connect('gallery.db')
    c = conn.cursor()
    
    # 检查是否已收藏
    c.execute('SELECT id FROM favorites WHERE user_id = ? AND image_id = ?', (user_id, image_id))
    existing = c.fetchone()
    
    if existing:
        # 取消收藏
        c.execute('DELETE FROM favorites WHERE id = ?', (existing[0],))
        favorited = False
    else:
        # 添加收藏
        c.execute('INSERT INTO favorites (user_id, image_id) VALUES (?, ?)', (user_id, image_id))
        favorited = True
    
    conn.commit()
    conn.close()
    
    return jsonify({'success': True, 'favorited': favorited})

# BUG FIX: 重写此路由以实现高效的分页、过滤和排序
@app.route('/api/favorites')
@login_required
def api_favorites():
    user_id = session['user_id']
    category = request.args.get('category')
    sort = request.args.get('sort', 'newest')
    page = int(request.args.get('page', 1))
    limit = int(request.args.get('limit', 12))
    offset = (page - 1) * limit
    
    # 直接调用高效的数据库函数
    favorites, total_count = get_user_favorites(
        user_id=user_id, 
        category=category, 
        sort=sort, 
        limit=limit, 
        offset=offset
    )
    
    return jsonify({
        'favorites': favorites,
        'total': total_count,
        'page': page,
        'limit': limit
    })


@app.route('/api/favorites-stats')
@login_required
def api_favorites_stats():
    user_id = session['user_id']

    conn = sqlite3.connect('gallery.db')
    c = conn.cursor()

    # 总收藏数
    c.execute('SELECT COUNT(id) FROM favorites WHERE user_id = ?', (user_id,))
    total = c.fetchone()[0]

    # 涉及分类数
    c.execute('''SELECT COUNT(DISTINCT i.category)
                  FROM favorites f
                  JOIN images i ON f.image_id = i.id
                  WHERE f.user_id = ?''', (user_id,))
    categories = c.fetchone()[0]

    # 涉及图集数
    c.execute('''SELECT COUNT(DISTINCT i.album)
                  FROM favorites f
                  JOIN images i ON f.image_id = i.id
                  WHERE f.user_id = ?''', (user_id,))
    albums = c.fetchone()[0]

    # 本周新增（最近7天）
    seven_days_ago = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d %H:%M:%S')
    c.execute('''SELECT COUNT(id)
                  FROM favorites
                  WHERE user_id = ? AND created_at >= ?''', (user_id, seven_days_ago))
    recent = c.fetchone()[0]

    conn.close()

    return jsonify({
        'total': total,
        'categories': categories,
        'albums': albums,
        'recent': recent
    })



@app.route('/api/favorites/clear', methods=['POST'])
@login_required
def api_clear_favorites():
    user_id = session['user_id']
    
    conn = sqlite3.connect('gallery.db')
    c = conn.cursor()
    c.execute('DELETE FROM favorites WHERE user_id = ?', (user_id,))
    conn.commit()
    conn.close()
    
    return jsonify({'success': True})

@app.route('/api/comments', methods=['GET', 'POST'])
def api_comments():
    if request.method == 'POST':
        if 'user_id' not in session:
            return jsonify({'success': False, 'error': '请先登录'}), 401
        
        data = request.get_json()
        image_id = data.get('image_id')
        content = data.get('content')
        user_id = session['user_id']

        if not all([image_id, content]):
            return jsonify({'success': False, 'error': '缺少必要参数'}), 400
        
        conn = sqlite3.connect('gallery.db')
        c = conn.cursor()
        c.execute('INSERT INTO comments (user_id, image_id, content) VALUES (?, ?, ?)',
                 (user_id, image_id, content))
        conn.commit()
        conn.close()
        
        return jsonify({'success': True})
    
    else: # GET
        image_id = request.args.get('image_id')
        if not image_id:
            return jsonify([])

        conn = sqlite3.connect('gallery.db')
        c = conn.cursor()
        c.execute('''SELECT c.content, c.created_at, u.username, u.avatar
                      FROM comments c
                      JOIN users u ON c.user_id = u.id
                      WHERE c.image_id = ?
                      ORDER BY c.created_at DESC''', (image_id,))
        comments = c.fetchall()
        conn.close()
        
        return jsonify([{
            'content': comment[0],
            'created_at': comment[1],
            'username': comment[2],
            'avatar': comment[3]
        } for comment in comments])

@app.route('/favorites')
@login_required
def favorites():
    return render_template_string(FAVORITES_TEMPLATE)

@app.route('/gallery')
def gallery():
    return redirect(url_for('index'))

# BUG FIX: 实现footer链接的实际内容页面
@app.route('/about')
def about():
    return render_template_string(ABOUT_TEMPLATE)

@app.route('/contact')
def contact():
    return render_template_string(CONTACT_TEMPLATE)

@app.route('/privacy')
def privacy():
    return render_template_string(PRIVACY_TEMPLATE)

@app.route('/terms')
def terms():
    return render_template_string(TERMS_TEMPLATE)

@app.route('/health')
def health_check():
    """简单的健康检查端点"""
    return jsonify({
        'status': 'healthy',
        'scan_completed': scan_progress.get('completed', False),
        'timestamp': datetime.now().isoformat()
    })

# BUG FIX: 隐私政策和使用条款模板
PRIVACY_TEMPLATE = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>隐私政策 - 春色写真馆</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 25%, #f0fff4 50%, #fff8dc 75%, #fdf5e6 100%);
            min-height: 100vh;
            color: #5d4e37;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
        }
        .header {
            text-align: center;
            margin-bottom: 3rem;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
        }
        .content {
            background: rgba(255, 255, 255, 0.9);
            padding: 2rem;
            border-radius: 20px;
            margin-bottom: 2rem;
        }
        h1 { color: #8b4513; margin-bottom: 1rem; }
        h2 { color: #cd853f; margin: 2rem 0 1rem 0; }
        .highlight {
            background: rgba(255, 182, 193, 0.1);
            padding: 1rem;
            border-radius: 10px;
            border-left: 4px solid #ff69b4;
            margin: 1rem 0;
        }
        .back-link {
            display: inline-block;
            padding: 0.8rem 1.5rem;
            background: linear-gradient(45deg, #ff69b4, #ffa500);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            transition: all 0.3s ease;
        }
        .back-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 105, 180, 0.4);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔒 隐私政策</h1>
            <p>我们重视并保护您的个人隐私</p>
            <small>最后更新：2024年7月</small>
        </div>

        <div class="content">
            <div class="highlight">
                <strong>重要提示：</strong>本软件为本地使用软件，所有数据均存储在用户本地设备上。
            </div>

            <h2>1. 本地数据存储</h2>
            <p>本软件采用本地数据存储方式：</p>
            <ul>
                <li><strong>用户数据：</strong>用户名、密码等信息存储在本地数据库</li>
                <li><strong>使用记录：</strong>浏览记录、收藏等数据仅保存在本地</li>
                <li><strong>图片文件：</strong>所有图片文件存储在用户指定的本地目录</li>
            </ul>

            <h2>2. 数据安全</h2>
            <p>由于采用本地存储：</p>
            <ul>
                <li>数据不会上传到任何远程服务器</li>
                <li>用户完全控制自己的数据</li>
                <li>无网络传输风险</li>
                <li>数据安全由用户设备安全保障</li>
            </ul>

            <h2>3. 隐私保护</h2>
            <div style="background: rgba(255, 99, 71, 0.1); padding: 1rem; border-radius: 10px; border-left: 4px solid #ff6347; margin: 1rem 0;">
                <strong>⚠️ 重要说明</strong>
                <ul>
                    <li>本软件不收集任何用户隐私信息</li>
                    <li>不进行任何形式的数据上传或共享</li>
                    <li>用户需自行保护本地数据安全</li>
                </ul>
            </div>

            <h2>4. 用户责任</h2>
            <p>用户需要：</p>
            <ul>
                <li>保护好本地设备和数据安全</li>
                <li>定期备份重要数据</li>
                <li>确保软件使用符合当地法规</li>
                <li>不将软件部署到网络环境</li>
            </ul>

            <h2>5. 开发者声明</h2>
            <div class="highlight">
                <strong>开发者承诺：</strong>本软件不会收集、传输或存储任何用户隐私信息到远程服务器。
            </div>

            <h2>6. 数据控制</h2>
            <p>用户拥有完全的数据控制权：</p>
            <ul>
                <li>可随时删除本地数据</li>
                <li>可自由迁移数据文件</li>
                <li>可完全卸载软件</li>
                <li>无需担心远程数据残留</li>
            </ul>
        </div>

        <div style="text-align: center;">
            <a href="/" class="back-link">🏠 返回首页</a>
        </div>
    </div>
</body>
</html>'''

TERMS_TEMPLATE = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>使用条款 - 春色写真馆</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 25%, #f0fff4 50%, #fff8dc 75%, #fdf5e6 100%);
            min-height: 100vh;
            color: #5d4e37;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
        }
        .header {
            text-align: center;
            margin-bottom: 3rem;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
        }
        .content {
            background: rgba(255, 255, 255, 0.9);
            padding: 2rem;
            border-radius: 20px;
            margin-bottom: 2rem;
        }
        h1 { color: #8b4513; margin-bottom: 1rem; }
        h2 { color: #cd853f; margin: 2rem 0 1rem 0; }
        .warning {
            background: rgba(255, 99, 71, 0.1);
            padding: 1rem;
            border-radius: 10px;
            border-left: 4px solid #ff6347;
            margin: 1rem 0;
        }
        .highlight {
            background: rgba(255, 182, 193, 0.1);
            padding: 1rem;
            border-radius: 10px;
            border-left: 4px solid #ff69b4;
            margin: 1rem 0;
        }
        .back-link {
            display: inline-block;
            padding: 0.8rem 1.5rem;
            background: linear-gradient(45deg, #ff69b4, #ffa500);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            transition: all 0.3s ease;
        }
        .back-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 105, 180, 0.4);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📋 使用条款</h1>
            <p>使用本服务前请仔细阅读以下条款</p>
            <small>最后更新：2024年7月</small>
        </div>

        <div class="content">
            <div class="warning">
                <strong>重要声明：</strong>本软件仅供本地个人使用，严禁部署到互联网或用于任何形式的网络服务。
            </div>

            <h2>1. 软件性质</h2>
            <p>春色写真馆是一个本地图片管理软件，提供：</p>
            <ul>
                <li>本地图片浏览和管理功能</li>
                <li>个人收藏管理</li>
                <li>本地数据存储</li>
                <li>离线使用体验</li>
            </ul>

            <h2>2. 使用限制</h2>
            <div class="warning">
                <strong>严格限制：</strong>
                <ul>
                    <li>仅限个人本地使用</li>
                    <li>禁止部署到任何网络服务器</li>
                    <li>禁止用于商业用途</li>
                    <li>禁止未经授权的分发或修改</li>
                    <li>禁止用于任何违法违规活动</li>
                </ul>
            </div>

            <h2>3. 用户责任</h2>
            <div class="highlight">
                <strong>用户必须：</strong>
                <ul>
                    <li>确保使用符合当地法律法规</li>
                    <li>自行承担所有使用风险</li>
                    <li>保护本地数据和设备安全</li>
                    <li>不将软件用于网络部署</li>
                    <li>尊重知识产权和他人权益</li>
                </ul>
            </div>

            <h2>4. 开发者免责</h2>
            <div class="warning">
                <strong>开发者声明：</strong>
                <ul>
                    <li>软件按"现状"提供，无任何保证</li>
                    <li>不承担任何直接或间接损失责任</li>
                    <li>不对软件误用承担法律责任</li>
                    <li>不提供任何形式的技术支持保证</li>
                    <li>保留随时停止开发的权利</li>
                </ul>
            </div>

            <h2>5. 知识产权</h2>
            <ul>
                <li>软件代码遵循开源协议</li>
                <li>用户需尊重第三方知识产权</li>
                <li>禁止商业化使用或分发</li>
                <li>修改和分发需遵循开源协议</li>
            </ul>

            <h2>6. 法律适用</h2>
            <p>用户使用本软件需遵守所在地区的法律法规。如有争议，用户自行承担法律后果。</p>

            <h2>7. 条款变更</h2>
            <p>开发者保留修改本条款的权利。继续使用软件即表示接受修改后的条款。</p>
        </div>

        <div style="text-align: center;">
            <a href="/" class="back-link">🏠 返回首页</a>
        </div>
    </div>
</body>
</html>'''

if __name__ == '__main__':
    # 确保下载目录和静态目录存在
    if not os.path.exists('downloaded'):
        os.makedirs('downloaded')
        categories = ['korea', 'cosplay', 'japan', 'gravure', 'chinese', 'thailand']
        for category in categories:
            os.makedirs(f'downloaded/{category}', exist_ok=True)
            
    if not os.path.exists('static'):
        os.makedirs('static')
        # BUG FIX: 创建默认的占位图片文件
        try:
            # 创建一个简单的SVG占位图
            placeholder_svg = '''<svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
                <rect width="200" height="200" fill="#f0f0f0"/>
                <text x="100" y="100" text-anchor="middle" dy=".3em" font-family="Arial" font-size="14" fill="#999">
                    图片加载失败
                </text>
            </svg>'''
            with open('static/placeholder.jpg', 'w', encoding='utf-8') as f:
                f.write(placeholder_svg)
        except Exception as e:
            print(f"创建占位图失败: {e}")

    # 初始化数据库
    print("🌸 初始化数据库...")
    init_db()
    
    # 启动扫描线程
    print("🔍 启动图片扫描...")
    scan_thread = Thread(target=scan_and_store_images)
    scan_thread.daemon = True
    scan_thread.start()
    
    # 启动应用
    print("\n🌸 春色写真馆启动中...")
    print("📱 访问地址: http://localhost:5000")
    print("✨ 功能特色:")
    print("   - 🎨 春色暧昧温暖主题设计")
    print("   - 📸 六大分类图片展示")
    print("   - 🔍 智能搜索和筛选")
    print("   - 👤 用户注册登录系统")
    print("   - ❤️ 收藏和评论功能")
    print("   - 📱 响应式移动端适配")
    print("   - ⭐ 评分和互动系统")
    print("   - 🚀 增量扫描和进度条")
    print("   - 💖 完整的收藏管理系统")
    print("   - 📂 图集详情页面和懒加载")
    print("\n💡 首次启动会显示扫描进度，可跳过直接进入...")
    print("\n🔧 最新改进内容:")
    print("   - ✅ [注册流程] 修复注册后自动登录并跳转到主页")
    print("   - ✅ [收藏系统] 简化为单一视图模式，提升用户体验")
    print("   - ✅ [图集分页] 实现图集列表分页，提升加载性能")
    print("   - ✅ [导航优化] Logo可点击返回首页，提升导航便利性")
    print("   - ✅ [图片查看] 图片直接点击查看，简化操作流程")
    print("   - ✅ [URL编码] 修复中文文件名显示问题")
    print("   - ✅ [跳过扫描] 优化跳过按钮功能和用户体验")
    print("   - ✅ [法律合规] 更新条款强调本地使用，保护开发者")
    print("   - ✅ [代码优化] 全面代码审查和性能优化")

    
    app.run(debug=True, host='0.0.0.0', port=5000)
